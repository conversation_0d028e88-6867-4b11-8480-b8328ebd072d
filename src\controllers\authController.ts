import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { UserData } from '../interfaces/express.interface';

interface AuthenticatedRequest extends Request {
    userData: UserData;
}

export class AuthController {
    private authService: AuthService;

    constructor(authService: AuthService) {
        this.authService = authService;
    }

    login = async (req: Request, res: Response) => {
        try {
            const { email, password } = req.body;
            const { token, user } = await this.authService.login(email, password);
            res.json({ token, user });
        } catch (error: any) {
            res.status(401).json({ message: error.message });
        }
    };

    register = async (req: Request, res: Response) => {
        try {
            const { 
                username, 
                email, 
                password, 
                user_reference, 
                user_icon, 
                name 
            } = req.body;

            const user = await this.authService.register({
                username,
                email,
                password,
                user_reference,
                user_icon,
                name
            });

            res.status(201).json({
                message: 'User registered successfully',
                user: {
                    messaging_user_id: user.id,
                    username: user.username,
                    email: user.email,
                    user_reference: user.user_reference,
                    user_icon: user.user_icon,
                    name: user.name
                }
            });
        } catch (error) {
            console.error('Registration error:', error);
            res.status(400).json({ 
                message: error instanceof Error ? error.message : 'Registration failed' 
            });
        }
    };

    refreshToken = async (req: Request, res: Response) => {
        try {
            const { refreshToken } = req.body;
            const newToken = await this.authService.refreshToken(refreshToken);
            res.json({ token: newToken });
        } catch (error: any) {
            res.status(401).json({ message: error.message });
        }
    };

    logout = async (req: AuthenticatedRequest, res: Response) => {
        try {
            if (!req.userData || !req.userData.id) {
                return res.status(401).json({ message: 'User not authenticated' });
            }
            await this.authService.logout(req.userData.id);
            res.status(200).json({ message: 'Logged out successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    updateUser = async (req: Request, res: Response) => {
        console.log(req.userData);
        try {
            const userId = req.userData?.id; // Assuming authenticated user's ID is available
            if (!userId) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const { 
                username, 
                email, 
                password, 
                user_reference, 
                user_icon, 
                name 
            } = req.body;

            const updatedUser = await this.authService.updateUser(userId, {
                username,
                email,
                password,
                user_reference,
                user_icon,
                name
            });

            res.status(200).json({
                message: 'User updated successfully',
                user: {
                    id: updatedUser.id,
                    username: updatedUser.username,
                    email: updatedUser.email,
                    user_reference: updatedUser.user_reference,
                    user_icon: updatedUser.user_icon,
                    name: updatedUser.name
                }
            });
        } catch (error) {
            console.error('User update error:', error);
            res.status(400).json({ 
                message: error instanceof Error ? error.message : 'User update failed' 
            });
        }
    };
}