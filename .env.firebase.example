# Firebase Configuration Examples
# Choose ONE of the following methods:

# ===== METHOD 1: Service Account JSON File (Recommended - All-in-One) =====
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json
# Database URL is automatically extracted from the JSON file!

# ===== METHOD 2: Service Account JSON as Environment Variable (All-in-One) =====
# FIREBASE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"your-project-id",...}
# Database URL is automatically extracted from the JSON content!

# ===== METHOD 3: Individual Environment Variables (Legacy) =====
# FIREBASE_PROJECT_ID=your-project-id
# FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
# FIREBASE_CLIENT_EMAIL=<EMAIL>
# FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Note: Only use ONE method above. The system will try them in order: 1 -> 2 -> 3
# Methods 1 & 2 automatically extract all info from the service account JSON!
