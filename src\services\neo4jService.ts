import neo4j, { Driver, Session } from 'neo4j-driver';
import config from '../config/neo4j';

/**
 * Neo4jService - Singleton service for managing Neo4j database connections
 * Handles connection pooling, session management, and recommendation queries
 */
export class Neo4jService {
    private driver: Driver;
    private static instance: Neo4jService;

    private constructor() {
        try {
            // Validate required configuration
            if (!config.neo4j.host || !config.neo4j.port || !config.neo4j.username || !config.neo4j.password) {
                throw new Error('Missing required Neo4j configuration');
            }

            // Initialize Neo4j driver with validated configuration
            this.driver = neo4j.driver(
                `bolt://${config.neo4j.host}:${config.neo4j.port}`,
                neo4j.auth.basic(config.neo4j.username, config.neo4j.password),
                {
                    maxConnectionLifetime: 3600000, // 1 hour
                    maxConnectionPoolSize: 50,
                    connectionAcquisitionTimeout: 20000, // 20 seconds
                }
            );

            console.log('Neo4j connection initialized:', {
                host: config.neo4j.host,
                port: config.neo4j.port,
                username: config.neo4j.username,
            });
        } catch (error) {
            console.error('Failed to initialize Neo4j connection:', error);
            throw error;
        }
    }

    /**
     * Get singleton instance of Neo4jService
     * @returns Neo4jService instance
     */
    public static getInstance(): Neo4jService {
        if (!Neo4jService.instance) {
            Neo4jService.instance = new Neo4jService();
        }
        return Neo4jService.instance;
    }

    /**
     * Get a new session for running queries
     * @returns Neo4j session configured with the default database
     */
    private getSession(): Session {
        if (!config.neo4j.database) {
            throw new Error('Neo4j database name is required');
        }
        return this.driver.session({
            database: config.neo4j.database
        });
    }

    /**
     * Close the Neo4j driver connection
     */
    public async close(): Promise<void> {
        await this.driver.close();
    }

    /**
     * Get recommended user references based on common connections
     * @param userReference - Current user's reference
     * @param limit - Maximum number of recommendations to return
     * @param offset - Number of recommendations to skip
     * @returns Array of recommended user references
     */
    public async getRecommendedUsers(userReference: string, limit: number = 10, offset: number = 0): Promise<string[]> {
        if (!userReference) {
            throw new Error('User reference is required');
        }

        const session = this.getSession();
        try {
            const result = await session.run(`
                MATCH (currentUser:Neo4jEntity {reference: $userReference})

                // Fetch 1st-level followers
                MATCH (currentUser)-[:FOLLOWS]->(potentialContact:Neo4jEntity)
                WHERE (potentialContact.is_active = true OR potentialContact.is_active IS NULL)
                AND potentialContact.icon IS NOT NULL
                AND potentialContact.handle IS NOT NULL

                WITH COLLECT(DISTINCT potentialContact) AS firstLevelContacts, currentUser

                // Fetch 2nd-level followers (friends of friends)
                MATCH (currentUser)-[:FOLLOWS]->(followedUser:Neo4jEntity)
                MATCH (followedUser)-[:FOLLOWS]->(potentialContact:Neo4jEntity)
                WHERE (potentialContact.is_active = true OR potentialContact.is_active IS NULL)
                AND potentialContact.icon IS NOT NULL
                AND potentialContact.handle IS NOT NULL
                AND potentialContact <> currentUser // Avoid self-suggestions

                WITH firstLevelContacts, COLLECT(DISTINCT potentialContact) AS secondLevelContacts

                // Merge first-level and second-level contacts
                WITH firstLevelContacts + secondLevelContacts AS allContacts
                UNWIND allContacts AS potentialContact

                // Sort before collecting
                WITH potentialContact
                ORDER BY potentialContact.created_date DESC
                RETURN COLLECT(DISTINCT potentialContact.reference) AS references
                SKIP $offset
                LIMIT $limit
            `, {
                userReference,
                limit: neo4j.int(limit),
                offset: neo4j.int(offset)
            });
            const recommendedUserReferences = result.records[0].get('references');
            console.log('Recommended user references:', recommendedUserReferences);
            return recommendedUserReferences;
        } catch (error) {
            console.error('Error getting recommended users:', error);
            throw error;
        } finally {
            await session.close();
        }
    }
}
