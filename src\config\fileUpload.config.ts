export const fileUploadConfig = {
    image: {
        maxFiles: 10,
        maxSizeKB: 2048, // 2MB
        compressionQuality: 80,
        allowedTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/bmp'
        ]
    },
    document: {
        maxSizeMB: 10,
        allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/vnd.ms-excel'
        ]
    },
    video: {
        maxSizeMB: 50, // 50MB max size for videos
        maxFiles: 2, // Maximum 2 videos at once
        allowedTypes: [
            'video/mp4',
            'video/quicktime',
            'video/x-msvideo',
            'video/mpeg',
            'video/3gpp',
            
        ],
        compression: {
            enabled: true,
            quality: 75,       // target compression quality
            minQuality: 50     // if the original quality is below this, skip compression
        }
    }
};
