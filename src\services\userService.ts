import { Pool } from 'pg';
import { DatabaseError } from '../utils/errors';
import { config } from '../config/env';

interface CreateUserParams {
    username: string;
    email: string;
    password_hash: string;
    user_icon: string;
    name: string;
}

export class UserService {
    private pool: Pool;

    constructor() {
        this.pool = new Pool(config.database);
    }

    async getUsers() {
        try {
            const { rows } = await this.pool.query(`
                SELECT id as messaging_user_id, username, user_reference, user_icon, name, email, created_at, updated_at, entity_type
                FROM users
                ORDER BY username
            `);
            return rows;
        } catch (error) {
            throw new DatabaseError('Error fetching users');
        }
    }

    async getUser(userId: string) {
        try {
            const { rows: [user] } = await this.pool.query(`
                SELECT id as messaging_user_id, username, user_reference, user_icon, name, email, created_at, updated_at, entity_type
                FROM users
                WHERE id = $1
            `, [userId]);
            
            return user;
        } catch (error) {
            throw new DatabaseError('Error fetching user');
        }
    }

    async getUserByEmail(email: string) {
        try {
            const { rows: [user] } = await this.pool.query(`
                SELECT id, username, user_reference, user_icon, name, email, password_hash, created_at, updated_at
                FROM users
                WHERE email = $1;
            `, [email]);
            
            return user || false;
        } catch (error) {
            throw new DatabaseError('Error fetching user by email');
        }
    }

    async createUser(params: CreateUserParams) {
        try {
            const { rows: [user] } = await this.pool.query(`
                INSERT INTO users (username, email, password_hash)
                VALUES ($1, $2, $3)
                RETURNING id, username, email, created_at, updated_at
            `, [params.username, params.email, params.password_hash]);

            return user;
        } catch (error) {
            throw new DatabaseError('Error creating user');
        }
    }

    async updateUser(userId: string, updates: Partial<CreateUserParams>) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            const { rows: [user] } = await client.query(`
                UPDATE users
                SET 
                    username = COALESCE($1, username),
                    email = COALESCE($2, email),
                    user_icon = COALESCE($3, user_icon),
                    name = COALESCE($4, name),
                    password_hash = COALESCE($5, password_hash),
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = $6
                RETURNING id AS messaging_user_id, username, email, name, user_icon, created_at, updated_at
            `, [
                updates.username,
                updates.email,
                updates.user_icon,
                updates.name,
                updates.password_hash,
                userId
            ]);

            await client.query('COMMIT');
            return user;
        } catch (error) {
            console.log(error);
            await client.query('ROLLBACK');
            throw new DatabaseError('Error updating user');
        } finally {
            client.release();
        }
    }

    async deleteUser(userId: string) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            await client.query(`
                DELETE FROM chat_members WHERE user_id = $1
            `, [userId]);

            await client.query(`
                DELETE FROM messages WHERE sender_id = $1
            `, [userId]);

            await client.query(`
                DELETE FROM users WHERE id = $1
            `, [userId]);

            await client.query('COMMIT');
        } catch (error) {
            console.log(error);
            await client.query('ROLLBACK');
            throw new DatabaseError('Error deleting user');
        } finally {
            client.release();
        }
    }
}