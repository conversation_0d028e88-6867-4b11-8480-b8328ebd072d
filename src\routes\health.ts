import { Router } from 'express';
import { Pool } from 'pg';
import { RedisClient } from 'redis';

export const healthRouter = Router();

healthRouter.get('/health', async (req, res) => {
  try {
    // Check database connection
    const pool = new Pool();
    await pool.query('SELECT 1');

    // Check Redis connection
    const redisUrl = process.env.REDIS_URL;
    if (!redisUrl) {
      throw new Error('REDIS_URL environment variable is not defined');
    }
    const redis = new RedisClient({ url: redisUrl });
    await new Promise((resolve, reject) => {
      redis.ping((err, reply) => {
        if (err) reject(err);
        else resolve(reply);
      });
    });

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        redis: 'connected'
      }
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      });
    } else {
      res.status(500).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'An unknown error occurred'
      });
    }
  }
});