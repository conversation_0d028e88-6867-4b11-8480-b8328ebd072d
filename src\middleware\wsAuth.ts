import { WebSocket } from 'ws';
import jwt from 'jsonwebtoken';
import config from '../config/app';

export const wsAuthMiddleware = async (
  ws: WebSocket,
  request: Request,
  client: any
) => {
  try {
    const token = request.headers.get('sec-websocket-protocol');
    if (!token) {
      ws.close(4001, 'Authentication required');
      return;
    }

    const decoded = jwt.verify(token, config.jwtSecret);
    if (typeof decoded === 'object' && 'userId' in decoded) {
      client.userId = decoded.userId;
    } else {
      ws.close(4002, 'Invalid token');
    }
    return true;
  } catch (error) {
    ws.close(4002, 'Invalid token');
    return false;
  }
};