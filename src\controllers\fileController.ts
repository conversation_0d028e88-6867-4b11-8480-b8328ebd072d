import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import fileUploadConfig from '../config/fileUpload';

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), fileUploadConfig.uploadDir);
const createUploadsDir = async () => {
    try {
        await fs.access(uploadsDir);
    } catch {
        await fs.mkdir(uploadsDir, { recursive: true });
    }
};

createUploadsDir();

export interface UploadedFile {
    original_name: string;
    file_name: string;
    file_type: 'image' | 'document' | 'video' | 'audio';
    file_size: number;
    url: string;
    mimetype: string;
    sender_user_id: string;
}

export const handleFileUpload = async (req: Request, res: Response) => {
    try {
        if (!req.userData) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        const files = req.files as Express.Multer.File[];
        
        if (!files || files.length === 0) {
            return res.status(400).json({ 
                success: false,
                error: 'No files uploaded' 
            });
        }

        const userId = req.userData.id as string;
        const uploadedFiles: UploadedFile[] = [];
        const imageFiles = files.filter(file => file.mimetype.startsWith('image/'));
        const documentFiles = files.filter(file => !file.mimetype.startsWith('image/'));

        // Validate image count
        if (imageFiles.length > fileUploadConfig.image.maxFiles) {
            return res.status(400).json({ 
                success: false,
                error: `Maximum ${fileUploadConfig.image.maxFiles} images allowed at once` 
            });
        }

        // Process each file
        for (const file of files) {
            const isImage = file.mimetype.startsWith('image/');
            const fileId = uuidv4();
            const ext = path.extname(file.originalname);
            const filename = `${fileId}${ext}`;
            const filePath = path.join(uploadsDir, filename);

            if (isImage) {
                // Compress image
                await sharp(file.path)
                    .resize(800, 800, {
                        fit: 'inside',
                        withoutEnlargement: true
                    })
                    .jpeg({ quality: fileUploadConfig.image.compressionQuality })
                    .toFile(filePath);

                const stats = await fs.stat(filePath);
                if (stats.size > fileUploadConfig.image.maxSizeKB * 1024) {
                    await fs.unlink(filePath);
                    return res.status(400).json({
                        success: false,
                        error: `Unable to compress image '${file.originalname}' below ${fileUploadConfig.image.maxSizeKB}KB`
                    });
                }
            } else {
                // Validate document size
                if (file.size > fileUploadConfig.document.maxSizeMB * 1024 * 1024) {
                    return res.status(400).json({
                        success: false,
                        error: `Document '${file.originalname}' exceeds maximum size of ${fileUploadConfig.document.maxSizeMB}MB`
                    });
                }
                await fs.rename(file.path, filePath);
            }

            const fileUrl = `/uploads/${filename}`;
            uploadedFiles.push({
                original_name: file.originalname,
                file_name: filename,
                file_type: isImage ? 'image' : 'document',
                file_size: (await fs.stat(filePath)).size,
                url: fileUrl,
                // full_url: `${process.env.BASE_URL}${fileUrl}`,
                mimetype: file.mimetype,
                sender_user_id: userId
            });
        }

        res.status(200).json({
            success: true,
            message: 'Files uploaded successfully',
            data: uploadedFiles
        });
    } catch (error) {
        console.error('File upload error:', error);
        res.status(500).json({ 
            success: false,
            error: 'Error processing file upload',
            message: (error as Error).message 
        });
    }
};
