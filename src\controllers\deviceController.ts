import { Request, Response } from 'express';
import { FirebaseService } from '../services/firebaseService';
import { validateDeviceToken, validateTestNotification } from '../validators/deviceValidator';
export class DeviceController {
    private firebaseService: FirebaseService;

    constructor() {
        this.firebaseService = FirebaseService.getInstance();
    }

    /**
     * Register or update FCM token for a user device
     */
    registerFCMToken = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.user_reference) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const { error } = validateDeviceToken(req.body);
            if (error) {
                return res.status(400).json({ message: error.details[0].message });
            }

            const { device_id, fcm_token, user_app_version } = req.body;
            const userReference = req.userData.user_reference;

            const success = await this.firebaseService.updateUserFCMToken(
                userReference,
                device_id,
                fcm_token,
                user_app_version
            );

            if (success) {
                res.status(200).json({ 
                    message: 'FCM token registered successfully',
                    user_reference: userReference,
                    device_id 
                });
            } else {
                res.status(500).json({ message: 'Failed to register FCM token' });
            }
        } catch (error: any) {
            console.error('Error registering FCM token:', error);
            res.status(500).json({ message: error.message });
        }
    };

    /**
     * Remove FCM token for a user device
     */
    removeFCMToken = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.user_reference) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const { device_id } = req.params;
            if (!device_id) {
                return res.status(400).json({ message: 'Device ID is required' });
            }

            const userReference = req.userData.user_reference;

            const success = await this.firebaseService.removeFCMToken(
                userReference,
                device_id
            );

            if (success) {
                res.status(200).json({ 
                    message: 'FCM token removed successfully',
                    user_reference: userReference,
                    device_id 
                });
            } else {
                res.status(500).json({ message: 'Failed to remove FCM token' });
            }
        } catch (error: any) {
            console.error('Error removing FCM token:', error);
            res.status(500).json({ message: error.message });
        }
    };

    /**
     * Get all FCM tokens for the current user
     */
    getUserFCMTokens = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.user_reference) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const userReference = req.userData.user_reference;
            const tokens = await this.firebaseService.getUserFCMTokens(userReference);

            res.status(200).json({ 
                user_reference: userReference,
                fcm_tokens: tokens,
                count: tokens.length
            });
        } catch (error: any) {
            console.error('Error fetching FCM tokens:', error);
            res.status(500).json({ message: error.message });
        }
    };

    /**
     * Send test notification to user's devices
     */
    sendTestNotification = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.user_reference) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const { error } = validateTestNotification(req.body);
            if (error) {
                return res.status(400).json({ message: error.details[0].message });
            }

            const { title, body } = req.body;

            const userReference = req.userData.user_reference;

            const success = await this.firebaseService.sendNotificationToUser(
                userReference,
                { title, body },
                { test: 'true', timestamp: new Date().toISOString() }
            );

            if (success) {
                res.status(200).json({ 
                    message: 'Test notification sent successfully',
                    user_reference: userReference
                });
            } else {
                res.status(500).json({ message: 'Failed to send test notification' });
            }
        } catch (error: any) {
            console.error('Error sending test notification:', error);
            res.status(500).json({ message: error.message });
        }
    };
}
