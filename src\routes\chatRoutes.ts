import { Router } from 'express';
import { Chat<PERSON>ontroller } from '../controllers/chatController';

const router = Router();
const chatController = new ChatController();

router.get('/get_all_chats', chatController.getAllChats);
router.get('/get_chats/v2', chatController.getChatsNew);
router.get('/get_chat_by_chatId/chatId=:chatId', chatController.getChatByChatId);
router.post('/create_chat', chatController.createChat);
router.put('/update_chat/chatId-:chatId', chatController.updateChat);
router.delete('/delete_chat/chatId=:chatId', chatController.deleteChat);
router.post('/chatId=:chatId/members', chatController.addMembers);
router.delete('/chatId-:chatId/members/userId=:userId', chatController.removeMember);
router.put('/chatId=:chatId/members/userId=:userId/role', chatController.updateMemberRole);
router.put('/chatId=:chatId/mute', chatController.updateMemberMuteStatus);
router.get('/admin-groups', chatController.getAdminGroupChats);
router.get('/check_direct_chat/userId=:userId', chatController.checkDirectChatExists);
router.get('/search_chats/searchQuery=:searchQuery/limit=:limit/offset=:offset', chatController.chatSearch);
export default router;