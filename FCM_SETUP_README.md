# Firebase Cloud Messaging (FCM) Setup Guide

This guide explains how to set up and use Firebase Cloud Messaging (FCM) push notifications in the chat-backend project.

## Overview

The FCM integration allows the chat application to send push notifications to offline users when they receive messages. The system automatically checks if users are connected to WebSocket and only sends FCM notifications to offline users.

## Features

- ✅ Send push notifications to offline users when they receive messages
- ✅ Automatic online/offline detection using WebSocket connections
- ✅ Respect user mute settings (no notifications for muted chats)
- ✅ Support for multiple devices per user
- ✅ FCM token management (register, update, remove)
- ✅ Test notification functionality
- ✅ Database migration for user device details

## Setup Instructions

### 1. Database Migration

First, run the database migration to create the `user_device_details` table:

```bash
npm run migrate up
```

This will create the table with the following structure:
- `user_device_details_id` (Primary Key)
- `user_reference` (Foreign Key to users table)
- `device_id` (Device identifier)
- `fcm_token` (Firebase Cloud Messaging token)
- `user_app_version` (App version)
- `created_at` and `updated_at` timestamps

### 2. Firebase Service Account Setup

1. Go to your Firebase Console
2. Navigate to Project Settings > Service Accounts
3. Generate a new private key (JSON file)
4. Save the JSON file to `./config/firebase-service-account.json`

### 3. Environment Configuration

Add the Firebase configuration to your `.env` file:

```env
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json
```

### 4. Data Migration (Optional)

If you have existing user device details in your main project, run the migration script:

```bash
cd scripts
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your database configurations
python migrate_user_device_details.py
```

## API Endpoints

### Device Management

#### Register/Update FCM Token
```http
POST /api/devices/fcm-token
Authorization: Bearer <token>
Content-Type: application/json

{
  "device_id": "device123",
  "fcm_token": "fcm_token_here",
  "user_app_version": "1.0.0"
}
```

#### Remove FCM Token
```http
DELETE /api/devices/fcm-token/:device_id
Authorization: Bearer <token>
```

#### Get User's FCM Tokens
```http
GET /api/devices/fcm-tokens
Authorization: Bearer <token>
```

#### Send Test Notification
```http
POST /api/devices/test-notification
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Test Notification",
  "body": "This is a test message"
}
```

## How It Works

### Message Flow

1. **User sends a message** via WebSocket or REST API
2. **Message is saved** to the database
3. **WebSocket broadcast** sends the message to all connected users in the chat
4. **FCM check** runs in parallel:
   - Gets all chat members except the sender
   - Checks if each member has muted the chat (skips if muted)
   - Checks if each member is online via WebSocket (skips if online)
   - Sends FCM notification to offline members

### Notification Content

- **Title**: Sender's name (or username if name not available)
- **Body**: Message content (truncated to 100 characters if longer)
- **Data**: Contains `chat_id`, `sender_id`, and `message_type`

### Platform-Specific Settings

The FCM service automatically includes platform-specific settings:

**Android:**
- High priority notification
- Default sound

**iOS (APNS):**
- Default sound
- Badge count increment

## Database Schema

### user_device_details Table

```sql
CREATE TABLE user_device_details (
    user_device_details_id SERIAL PRIMARY KEY,
    user_reference VARCHAR(255) NOT NULL REFERENCES users(user_reference) ON DELETE CASCADE,
    device_id VARCHAR(30),
    fcm_token VARCHAR(300),
    user_app_version VARCHAR(10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Indexes

- `idx_user_device_details_user_reference` on `user_reference`
- `idx_user_device_details_fcm_token` on `fcm_token`
- `idx_user_device_details_device_id` on `device_id`

## Client Integration

### Mobile App Integration

1. **Initialize FCM** in your mobile app
2. **Get FCM token** when app starts or user logs in
3. **Register token** with the backend using the `/api/devices/fcm-token` endpoint
4. **Update token** when it refreshes (FCM tokens can change)
5. **Remove token** when user logs out

### Example Client Code (React Native)

```javascript
import messaging from '@react-native-firebase/messaging';

// Get FCM token
const getFCMToken = async () => {
  const token = await messaging().getToken();
  return token;
};

// Register token with backend
const registerFCMToken = async (deviceId, fcmToken, appVersion) => {
  const response = await fetch('/api/devices/fcm-token', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      device_id: deviceId,
      fcm_token: fcmToken,
      user_app_version: appVersion,
    }),
  });
  return response.json();
};
```

## Troubleshooting

### Common Issues

1. **Firebase not initialized**: Check that `FIREBASE_SERVICE_ACCOUNT_PATH` is set correctly
2. **Invalid FCM tokens**: The service automatically logs invalid tokens for cleanup
3. **No notifications received**: Check if user is online (connected to WebSocket)
4. **Notifications for muted chats**: Verify that `is_muted` is set correctly in `chat_members`

### Logs

The service provides detailed logging for:
- Firebase initialization
- FCM token registration/removal
- Notification sending (success/failure)
- Online/offline user detection
- Muted chat detection

### Testing

Use the test notification endpoint to verify FCM setup:

```bash
curl -X POST http://localhost:4000/api/devices/test-notification \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"title": "Test", "body": "Testing FCM notifications"}'
```

## Security Considerations

- FCM tokens are stored securely in the database
- Only authenticated users can manage their own FCM tokens
- Invalid tokens are automatically detected and logged
- User privacy is respected (muted chats, online status)

## Performance

- FCM notifications are sent asynchronously (non-blocking)
- Database queries are optimized with proper indexes
- WebSocket connection checking is efficient (in-memory)
- Failed notifications don't affect message delivery
