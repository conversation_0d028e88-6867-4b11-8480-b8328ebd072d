{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Testing RabbitMQ Consumer\n", "\n", "This notebook tests the RabbitMQ consumer functionality for the AI message processing system."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pika\n", "import json\n", "import logging\n", "from decouple import config\n", "import requests\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# RabbitMQ Consumer Class\n", "class RabbitMQConsumer:\n", "    def __init__(self):\n", "        self.connection = None\n", "        self.channel = None\n", "        self.queue_name = 'store_ai_messages_queue'\n", "\n", "    def connect(self):\n", "        \"\"\"Establish a connection to RabbitMQ\"\"\"\n", "        try:\n", "            credentials = pika.PlainCredentials(\n", "                username=config('RABBITMQ_USERNAME', 'rmq_local_mj'),\n", "                password=config('RABBITMQ_PASSWORD', 'swadesicrmq')\n", "            )\n", "            parameters = pika.ConnectionParameters(\n", "                host=config('RABBITMQ_HOST', '**************'),\n", "                port=int(config('RABBITMQ_PORT', '5672')),\n", "                credentials=credentials\n", "            )\n", "            self.connection = pika.BlockingConnection(parameters)\n", "            self.channel = self.connection.channel()\n", "            self.channel.queue_declare(queue=self.queue_name, durable=True)\n", "            logger.info(\"Successfully connected to RabbitMQ\")\n", "            return True\n", "        except Exception as e:\n", "            logger.error(f\"Error connecting to RabbitMQ: {e}\")\n", "            return False\n", "\n", "    def close(self):\n", "        \"\"\"Close the RabbitMQ connection\"\"\"\n", "        if self.channel:\n", "            self.channel.close()\n", "        if self.connection:\n", "            self.connection.close()\n", "\n", "    def get_message(self):\n", "        \"\"\"Get a single message from the queue\"\"\"\n", "        try:\n", "            method_frame, header_frame, body = self.channel.basic_get(\n", "                queue=self.queue_name,\n", "                auto_ack=False\n", "            )\n", "            if method_frame:\n", "                message = json.loads(body)\n", "                return method_frame.delivery_tag, message\n", "            return None, None\n", "        except Exception as e:\n", "            logger.error(f\"Error getting message from RabbitMQ: {e}\")\n", "            return None, None\n", "\n", "    def acknowledge_message(self, delivery_tag):\n", "        \"\"\"Acknowledge that a message has been processed\"\"\"\n", "        try:\n", "            self.channel.basic_ack(delivery_tag=delivery_tag)\n", "            return True\n", "        except Exception as e:\n", "            logger.error(f\"Error acknowledging message: {e}\")\n", "            return False"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:pika.adapters.utils.connection_workflow:Pika version 1.3.2 connecting to ('**************', 5672)\n", "INFO:pika.adapters.utils.io_services_utils:Socket connected: <socket.socket fd=1620, family=2, type=1, proto=6, laddr=('***********', 57651), raddr=('**************', 5672)>\n", "INFO:pika.adapters.utils.connection_workflow:Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CFB5390>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CFB5390> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>).\n", "INFO:pika.adapters.utils.connection_workflow:AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CFB5390> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.utils.connection_workflow:AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CFB5390> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.blocking_connection:Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CFB5390> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.blocking_connection:Created channel=1\n", "INFO:__main__:Successfully connected to RabbitMQ\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Connection status: Connected\n"]}], "source": ["# Test Connection\n", "consumer = RabbitMQConsumer()\n", "connected = consumer.connect()\n", "print(f\"Connection status: {'Connected' if connected else 'Failed to connect'}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Received message:\n", "{\n", "  \"user_id\": \"a3d19c8b-b5a5-4f81-ac57-ae4a2245881e\",\n", "  \"sai_user_id\": \"50aec3eb-4edb-4ccc-82ab-3ce49f211afc\",\n", "  \"chat_id\": \"1603f95a-c8ea-40d0-b27e-5a228b60593d\",\n", "  \"message\": \"Hello store AI, what products do you have?\",\n", "  \"sai_reference\": \"SAI_1721930951916\",\n", "  \"user_reference\": \"U1234567890146\",\n", "  \"sequence_number\": 8,\n", "  \"created_at\": \"2025-03-14T14:32:07.113Z\"\n", "}\n", "\n", "Delivery tag: 1\n"]}], "source": ["# Test Getting a Message\n", "try:\n", "    delivery_tag, message = consumer.get_message()\n", "    if message:\n", "        print(\"Received message:\")\n", "        print(json.dumps(message, indent=2))\n", "        print(f\"\\nDelivery tag: {delivery_tag}\")\n", "    else:\n", "        print(\"No messages in queue\")\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Message Acknowledgment\n", "if delivery_tag:\n", "    ack_success = consumer.acknowledge_message(delivery_tag)\n", "    print(f\"Message acknowledgment {'successful' if ack_success else 'failed'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cleanup\n", "consumer.close()\n", "print(\"Connection closed\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'user_id': 'a3d19c8b-b5a5-4f81-ac57-ae4a2245881e',\n", " 'sai_user_id': '50aec3eb-4edb-4ccc-82ab-3ce49f211afc',\n", " 'chat_id': '1603f95a-c8ea-40d0-b27e-5a228b60593d',\n", " 'message': 'Hello store AI, what products do you have?',\n", " 'sai_reference': 'SAI_1721930951916',\n", " 'user_reference': 'U1234567890146',\n", " 'sequence_number': 8,\n", " 'created_at': '2025-03-14T14:32:07.113Z'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["message"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Message Processing Flow\n", "\n", "The following cell demonstrates the complete message processing flow:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def process_message():\n", "    consumer = RabbitMQConsumer()\n", "    if not consumer.connect():\n", "        print(\"Failed to connect to RabbitMQ\")\n", "        return\n", "\n", "    try:\n", "        delivery_tag, message = consumer.get_message()\n", "        \n", "        if not message:\n", "            print(\"No messages in queue\")\n", "            return\n", "\n", "        print(\"\\nProcessing message:\")\n", "        print(json.dumps(message, indent=2))\n", "\n", "        # Simulate processing\n", "        data = {\n", "            \"chat_id\": message[\"chat_id\"],\n", "            \"content\": \"hello ji reply from store ai\",\n", "            \"message_type\": \"text\",\n", "            \"metadata\": {},\n", "            \"attachments\": []\n", "        }\n", "        \n", "        # Make the API call\n", "        response = requests.post(\n", "            f\"{config('API_URL')}/api/messages/send_message\",\n", "            headers={\n", "                \"Authorization\": f\"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.qYgJYIjJcY74HfG3qPH_JK-r9W1qo6VxabJfguo2H0w\",\n", "                \"Content-Type\": \"application/json\"\n", "            },\n", "            json=data\n", "        )\n", "        \n", "        if response.status_code == 201:\n", "            logger.info(\"Message sent successfully.\")\n", "        else:\n", "            logger.error(f\"Failed to send message: {response.status_code} - {response.text}\")\n", "        \n", "        # Acknowledge message\n", "        if consumer.acknowledge_message(delivery_tag):\n", "            print(\"Message successfully acknowledged\")\n", "        else:\n", "            print(\"Failed to acknowledge message\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error processing message: {e}\")\n", "    finally:\n", "        consumer.close()\n", "        print(\"\\nConnection closed\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:pika.adapters.utils.connection_workflow:Pika version 1.3.2 connecting to ('**************', 5672)\n", "INFO:pika.adapters.utils.io_services_utils:Socket connected: <socket.socket fd=1452, family=2, type=1, proto=6, laddr=('***********', 57739), raddr=('**************', 5672)>\n", "INFO:pika.adapters.utils.connection_workflow:Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>).\n", "INFO:pika.adapters.utils.connection_workflow:AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.utils.connection_workflow:AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.blocking_connection:Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>\n", "INFO:pika.adapters.blocking_connection:Created channel=1\n", "INFO:__main__:Successfully connected to RabbitMQ\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Processing message:\n", "{\n", "  \"user_id\": \"a3d19c8b-b5a5-4f81-ac57-ae4a2245881e\",\n", "  \"sai_user_id\": \"50aec3eb-4edb-4ccc-82ab-3ce49f211afc\",\n", "  \"chat_id\": \"1603f95a-c8ea-40d0-b27e-5a228b60593d\",\n", "  \"message\": \"Hello store AI, what products do you have?\",\n", "  \"sai_reference\": \"SAI_1721930951916\",\n", "  \"user_reference\": \"U1234567890146\",\n", "  \"sequence_number\": 11,\n", "  \"created_at\": \"2025-03-14T14:46:19.534Z\"\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:__main__:Failed to send message: 500 - {\"message\":\"Error creating message: Sender is not a member of the chat\"}\n", "INFO:pika.channel:Closing channel (0): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Message successfully acknowledged\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:pika.channel:Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001C91CDBFAD0> params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>>\n", "INFO:pika.adapters.blocking_connection:Closing connection (200): Normal shutdown\n", "INFO:pika.connection:Closing connection (200): 'Normal shutdown'\n", "INFO:pika.adapters.utils.io_services_utils:Aborting transport connection: state=1; <socket.socket fd=1452, family=2, type=1, proto=6, laddr=('***********', 57739), raddr=('**************', 5672)>\n", "INFO:pika.adapters.utils.io_services_utils:_AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=1452, family=2, type=1, proto=6, laddr=('***********', 57739), raddr=('**************', 5672)>\n", "INFO:pika.adapters.utils.io_services_utils:Deactivating transport: state=1; <socket.socket fd=1452, family=2, type=1, proto=6, laddr=('***********', 57739), raddr=('**************', 5672)>\n", "INFO:pika.connection:AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'\n", "INFO:pika.connection:Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'\n", "INFO:pika.adapters.utils.io_services_utils:Closing transport socket and unlinking: state=3; <socket.socket fd=1452, family=2, type=1, proto=6, laddr=('***********', 57739), raddr=('**************', 5672)>\n", "INFO:pika.adapters.blocking_connection:User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=************** port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Connection closed\n"]}], "source": ["\n", "# Run the processing flow\n", "process_message()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}