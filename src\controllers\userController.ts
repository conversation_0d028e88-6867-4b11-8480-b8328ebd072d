import { Request, Response } from 'express';
import { UserService } from '../services/userService';

export class UserController {
    private userService: UserService;

    constructor() {
        this.userService = new UserService();
    }

    getUsers = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const users = await this.userService.getUsers();
            res.json(users);
        } catch (error) {
            res.status(500).json({ message: 'Error fetching users' });
        }
    };

    getUser = async (req: Request, res: Response) => {
        
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            // Prioritize userId from params, then from query
            const userId = req.params.userId || req.query.userId as string;
            
            if (!userId) {
                return res.status(400).json({ message: 'User ID is required' });
            }

            const user = await this.userService.getUser(userId);
            
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            
            res.json(user);
        } catch (error) {
            res.status(500).json({ message: 'Error fetching user' });
        }
    };

    updateUser = async (req: Request, res: Response) => {
        try {
            const userId = req.params.userId;
            const updateData = req.body;
            
            const updatedUser = await this.userService.updateUser(userId, updateData);
            
            res.json(updatedUser);
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Error updating user' });
        }
    };

    deleteUser = async (req: Request, res: Response) => {
        try {
            const userId = req.params.userId;
            
            await this.userService.deleteUser(userId);
            
            res.status(204).json({ message: 'User deleted successfully' });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Error deleting user' });
        }
    };
}