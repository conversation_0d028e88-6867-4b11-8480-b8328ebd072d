import { Router } from 'express';
import { <PERSON>ceController } from '../controllers/deviceController';

const router = Router();
const deviceController = new DeviceController();

// FCM Token Management
router.post('/fcm-token', deviceController.registerFCMToken);
router.delete('/fcm-token/:device_id', deviceController.removeFCMToken);
router.get('/fcm-tokens', deviceController.getUserFCMTokens);

// Test notification
router.post('/test-notification', deviceController.sendTestNotification);

export default router;
