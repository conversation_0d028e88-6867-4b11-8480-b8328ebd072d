export interface FileUploadConfig {
    uploadDir: string;
    image: {
        maxFiles: number;
        maxSizeKB: number;
        compressionQuality: number;
        allowedTypes: string[];
    };
    document: {
        maxSizeMB: number;
    };
}

const fileUploadConfig: FileUploadConfig = {
    uploadDir: 'uploads',
    image: {
        maxFiles: 4,
        maxSizeKB: 500,
        compressionQuality: 80,
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    document: {
        maxSizeMB: 10
    }
};

export default fileUploadConfig;
