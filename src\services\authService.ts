import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Pool } from 'pg';
import { User, UserCreateDTO, UserUpdateDTO } from '../models/user';
import { config } from '../config/env';
import { UserService } from './userService';
import { DatabaseError } from '../utils/errors';

export class AuthService {
    private pool: Pool;
    private userService: UserService;

    constructor(pool: Pool) {
        this.pool = pool;
        this.userService = new UserService();
    }

    async login(email: string, password: string) {
        const client = await this.pool.connect();
        try {
            const { rows: [user] } = await client.query(
                'SELECT * FROM users WHERE email = $1',
                [email]
            );

            if (!user) {
                throw new Error('Invalid credentials');
            }

            const isValidPassword = await bcrypt.compare(password, user.password_hash);
            if (!isValidPassword) {
                throw new Error('Invalid credentials');
            }

            const token = jwt.sign(
                { id: user.id, email: user.email, user_reference: user.user_reference },
                config.jwt.secret
            );
            
            return { 
                token, 
                user: {
                    messaging_user_id: user.id,
                    username: user.username,
                    email: user.email,
                    user_reference: user.user_reference,
                    user_icon: user.user_icon,
                    name: user.name
                }
            };
        } finally {
            client.release();
        }
    }

    async register(userData: UserCreateDTO): Promise<User> {
        const client = await this.pool.connect();

        try {
            await client.query('BEGIN');

            // Check if user already exists
            const existingUserResult = await client.query(
                'SELECT * FROM users WHERE username = $1 OR email = $2',
                [userData.username, userData.email]
            );

            if (existingUserResult.rows.length > 0) {
                throw new Error('Username or email already exists');
            }

            // Hash password
            const hashedPassword = await bcrypt.hash(userData.password, 10);

            // Insert new user
            const { rows: [newUser] } = await client.query(
                `INSERT INTO users 
                (username, email, password_hash, user_reference, user_icon, name, entity_type) 
                VALUES ($1, $2, $3, $4, $5, $6, $7) 
                RETURNING *`,
                [
                    userData.username, 
                    userData.email, 
                    hashedPassword,
                    userData.user_reference || null,
                    userData.user_icon || null,
                    userData.name || null,
                    userData.user_reference?.startsWith('SAI_') ? 'STORE_AI' : userData.user_reference?.startsWith('S') ? 'STORE' : 'USER'
                ]
            );

            await client.query('COMMIT');
            return newUser;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    async updateUser(userId: string, userData: UserUpdateDTO): Promise<User> {
        const client = await this.pool.connect();

        try {
            await client.query('BEGIN');

            // Prepare update fields
            const updateFields = [];
            const values = [];
            let paramCount = 1;

            if (userData.username) {
                updateFields.push(`username = $${paramCount}`);
                values.push(userData.username);
                paramCount++;
            }

            if (userData.email) {
                updateFields.push(`email = $${paramCount}`);
                values.push(userData.email);
                paramCount++;
            }

            if (userData.password) {
                const hashedPassword = await bcrypt.hash(userData.password, 10);
                updateFields.push(`password_hash = $${paramCount}`);
                values.push(hashedPassword);
                paramCount++;
            }

            if (userData.user_reference) {
                updateFields.push(`user_reference = $${paramCount}`);
                values.push(userData.user_reference);
                paramCount++;
            }

            if (userData.user_icon) {
                updateFields.push(`user_icon = $${paramCount}`);
                values.push(userData.user_icon);
                paramCount++;
            }

            if (userData.name) {
                updateFields.push(`name = $${paramCount}`);
                values.push(userData.name);
                paramCount++;
            }

            // Add user ID as the last parameter
            values.push(userId);

            // Construct and execute update query
            const updateQuery = `
                UPDATE users 
                SET ${updateFields.join(', ')}
                WHERE id = $${paramCount}
                RETURNING *
            `;

            const { rows: [updatedUser] } = await client.query(updateQuery, values);

            await client.query('COMMIT');
            return updatedUser;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    async refreshToken(refreshToken: string) {
        try {
            const decoded = jwt.verify(refreshToken, config.jwt.secret) as any;
            const token = jwt.sign(
                { id: decoded.id, email: decoded.email, user_reference: decoded.user_reference },
                config.jwt.secret
            );
            return { token };
        } catch (error) {
            throw new Error('Invalid refresh token');
        }
    }

    async logout(userId: string) {
        // Implement any necessary cleanup
        // For example, invalidating tokens in a blacklist
        return true;
    }

    private generateToken(userId: string): string {
        const payload = { id: userId };
        const token = jwt.sign(payload, config.jwt.secret, {});
        return token;
    }
}