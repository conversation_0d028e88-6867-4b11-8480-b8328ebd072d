import { Request, Response } from 'express';
import { ChatService } from '../services/chatService';

export class Chat<PERSON><PERSON>roller {
    private chatService: ChatService;

    constructor() {
        this.chatService = new ChatService();
    }

    getAllChats = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const chats = await this.chatService.getUserChats(req.userData.id);
            res.json(chats);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    getChatsNew = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const chatType = req.query.chatType as 'ACTIVE' | 'REQUEST';
            const limit = parseInt(req.query.limit as string) || 10;
            const offset = parseInt(req.query.offset as string) || 0;

            if (!['ACTIVE', 'REQUEST'].includes(chatType)) {
                return res.status(400).json({ message: 'Invalid chat type. Must be ACTIVE or REQUEST' });
            }
            console.log('user_reference', req.userData.email.split('@')[0]);
            const chats = await this.chatService.getUserChatsNew(
                req.userData.id,
                req.userData.email.split('@')[0],
                chatType,
                limit,
                offset
            );
            res.json(chats);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    getChatByChatId = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chatId } = req.params;
            const chat = await this.chatService.getChatByChatId(chatId, req.userData.id);
            if (!chat) {
                return res.status(404).json({ message: 'Chat not found' });
            }
            res.json(chat);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    createChat = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chat_type, member_ids, chat_name, message_access } = req.body;

            // Validate message_access if provided
            if (message_access && !['ADMIN_ONLY', 'MEMBERS_ONLY', 'ALL'].includes(message_access)) {
                return res.status(400).json({
                    message: 'Invalid message_access value. Must be ADMIN_ONLY, MEMBERS_ONLY, or ALL'
                });
            }

            const { chat, newly_created } = await this.chatService.createChat({
                chatType: chat_type,
                creatorId: req.userData.id,
                memberIds: member_ids,
                chatName: chat_name,
                messageAccess: message_access
            });
            if (newly_created) {
                res.status(201).json({ mesage:'Chat created successfully' , chat });
            } else {
                res.status(200).json({ message:'Chat already exists' , chat});
            }
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    updateChat = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chatId } = req.params;
            const chat = await this.chatService.updateChat(chatId, req.body, req.userData.id);
            res.json(chat);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    deleteChat = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chatId } = req.params;
            await this.chatService.deleteChat(chatId, req.userData.id);
            res.status(200).json({ message: 'Chat deleted successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    addMembers = async (req: Request, res: Response) => {
        try {
            const { chatId } = req.params;
            const { memberIds, role_type } = req.body;

            // Validate role_type if provided
            if (role_type && !['MEMBER', 'ADMIN'].includes(role_type)) {
                return res.status(400).json({
                    message: 'Invalid role_type value. Must be MEMBER or ADMIN'
                });
            }

            await this.chatService.addMembers(chatId, memberIds, role_type);
            res.status(200).json({
                message: 'Members added successfully',
                role_assigned: role_type || 'default'
            });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    removeMember = async (req: Request, res: Response) => {
        try {
            const { chatId, userId } = req.params;
            await this.chatService.removeMember(chatId, userId);
            res.status(200).json({ message: 'Member removed successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    updateMemberRole = async (req: Request, res: Response) => {
        try {
            const { chatId, userId } = req.params;
            const { role_type } = req.body;

            if (!['MEMBER', 'ADMIN'].includes(role_type)) {
                return res.status(400).json({ message: 'Invalid role type. Must be MEMBER or ADMIN' });
            }

            await this.chatService.updateMemberRole(chatId, userId, role_type);
            res.status(200).json({ message: 'Member role updated successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    updateMemberMuteStatus = async (req: Request, res: Response) => {
        try {
            const { chatId } = req.params;
            const { is_muted } = req.body;

            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            if (typeof is_muted !== 'boolean') {
                return res.status(400).json({ message: 'is_muted must be a boolean value' });
            }

            await this.chatService.updateMemberMuteStatus(chatId, req.userData.id, is_muted);
            res.status(200).json({ message: 'Mute status updated successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    getAdminGroupChats = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }

            const adminChatIds = await this.chatService.getAdminGroupChats(req.userData.id);
            res.status(200).json({
                message: 'Admin group chat IDs retrieved successfully',
                data: adminChatIds,
                count: adminChatIds.length
            });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    checkDirectChatExists = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { userId} = req.params;
            const chat = await this.chatService.checkDirectChatExists(req.userData.id, userId);
            if (chat) {
                return res.status(200).json({message: 'Chat already exists', chat });
            } else {
                return res.status(404).json({ message: 'No chat found between these users.' });
            }
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };
    chatSearch = async (req: Request, res: Response) => {
        try {
            if (!req.userData) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const searchQuery = req.params.searchQuery;
            const limit = parseInt(req.params.limit) || 10;
            const offset = parseInt(req.params.offset) || 0;
            const chats = await this.chatService.chatSearch(searchQuery, limit, offset, req.userData.id);
            res.json(chats);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };
}