export class CustomError extends Error {
    constructor(public message: string, public statusCode: number) {
        super(message);
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}

export class DatabaseError extends CustomError {
    constructor(message: string) {
        super(message, 500);
    }
}

export class ValidationError extends CustomError {
    constructor(message: string) {
        super(message, 400);
    }
}

export class AuthenticationError extends CustomError {
    constructor(message: string = 'Authentication failed') {
        super(message, 401);
    }
}

export class ForbiddenError extends CustomError {
    constructor(message: string = 'Access denied') {
        super(message, 403);
    }
}

export class NotFoundError extends CustomError {
    constructor(message: string) {
        super(message, 404);
    }
}