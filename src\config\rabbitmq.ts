/**
 * RabbitMQ configuration settings
 * Contains connection details and queue configurations for message broker
 */
export const rabbitmqConfig = {
    connection: {
        protocol: 'amqp',
        hostname: process.env.RABBITMQ_HOST || 'localhost',
        port: parseInt(process.env.RABBITMQ_PORT || '5672'),  // Default AMQP port, not the management port (15672)
        username: process.env.RABBITMQ_USERNAME || 'guest',  // Default RabbitMQ username
        password: process.env.RABBITMQ_PASSWORD || 'guest',  // Default RabbitMQ password
    },
    queues: {
        aiMessages: process.env.RABBITMQ_QUEUE || 'ai_messages_local'  // Queue for AI message processing
    },
    exchanges: {
        aiMessages: process.env.RABBITMQ_EXCHANGE || 'ai_messages_local'  // Exchange for AI message routing
    },
    routingKeys: {
        aiMessages: process.env.RABBITMQ_KEY || 'ai_messages_local' // Routing key for AI messages
    }
};
