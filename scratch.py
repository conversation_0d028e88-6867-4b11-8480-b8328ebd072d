import pika
import json
import logging
from celery import shared_task
import requests
from decouple import config
from psycopg2 import sql
import psycopg2

logger = logging.getLogger(__name__)

class RabbitMQConsumer:
    def __init__(self):
        self.connection = None
        self.channel = None
        self.queue_name = 'store_ai_messages_queue'

    def connect(self):
        """Establish a connection to RabbitMQ"""
        try:
            credentials = pika.PlainCredentials(
                username=config('RABBITMQ_USERNAME', 'guest'),
                password=config('RABBITMQ_PASSWORD', 'guest')
            )
            parameters = pika.ConnectionParameters(
                host=config('RABBITMQ_HOST', 'localhost'),
                port=int(config('RABBITMQ_PORT', '5672')),
                credentials=credentials
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            self.channel.queue_declare(queue=self.queue_name, durable=True)
            logger.info("Successfully connected to RabbitM<PERSON>")
            return True
        except Exception as e:
            logger.error(f"Error connecting to RabbitMQ: {e}")
            return False

    def close(self):
        """Close the RabbitMQ connection"""
        if self.channel:
            self.channel.close()
        if self.connection:
            self.connection.close()

    def get_message(self):
        """Get a single message from the queue"""
        try:
            method_frame, header_frame, body = self.channel.basic_get(
                queue=self.queue_name,
                auto_ack=False
            )
            if method_frame:
                message = json.loads(body)
                return method_frame.delivery_tag, message
            return None, None
        except Exception as e:
            logger.error(f"Error getting message from RabbitMQ: {e}")
            return None, None

    def acknowledge_message(self, delivery_tag):
        """Acknowledge that a message has been processed"""
        try:
            self.channel.basic_ack(delivery_tag=delivery_tag)
            return True
        except Exception as e:
            logger.error(f"Error acknowledging message: {e}")
            return False

@shared_task(bind=True)
def process_store_AI_responses(self):
    logger.info("Entered process_store_AI_responses function")
    
    # Initialize RabbitMQ consumer
    consumer = RabbitMQConsumer()
    if not consumer.connect():
        logger.error("Failed to connect to RabbitMQ")
        return

    try:
        # Get a message from the queue
        delivery_tag, message = consumer.get_message()
        
        if not message:
            logger.info("No messages in queue")
            return

        # Step 2: Prepare the API request
        data = {
            "store_reference": message["sai_reference"].replace("SAI_", ""),
            "query": message["message"],
            "chat_history": []
        }
        
        # Step 3: Make the API call
        response = requests.post(
            f"{config('STORE_AI_API_URL')}chat/query",
            headers={
                'accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data=json.dumps(data)
        )
        
        # Step 4: Handle the response
        if response.status_code == 200:
            api_response = response.json()
            logger.info(f"Response from AI API: {api_response}")
            query_reply = api_response.get('answer', '')

            # Send the reply to chat via chat api
            message_sent = send_ai_response_to_chat(
                message["chat_id"], 
                message["sai_reference"], 
                query_reply
            )
            
            if message_sent:
                # Acknowledge the message only if processing was successful
                if consumer.acknowledge_message(delivery_tag):
                    logger.info(f"Successfully processed and acknowledged message")
                else:
                    logger.error("Failed to acknowledge message")
            else:
                logger.error(f"Failed to send AI response message: {query_reply}")
        else:
            logger.error(f"Failed to get response: {response.status_code} - {response.text}")

    except Exception as e:
        logger.error(f"Error processing message: {e}")
    finally:
        consumer.close()

# Helper function (unchanged)
def send_ai_response_to_chat(chat_id, SAI_reference, reply_text):
    try:
        store_reference = SAI_reference.replace("SAI_", "")
        store = Store.objects.get(store_reference=store_reference)
        store_ai_messaging_token = store.store_ai_messaging_token 
        
        # Prepare the API request data
        data = {
            "chatId": chat_id,
            "content": reply_text,
            "messageType": "text",
            "metadata": {},
            "attachments": []
        }
        
        # Make the API call
        response = requests.post(
            f"{config('MESSAGING_API_URL')}api/messages",
            headers={
                "Authorization": f"Bearer {store_ai_messaging_token}",
                "Content-Type": "application/json"
            },
            json=data
        )
        
        if response.status_code == 201:
            logger.info("Message sent successfully.")
            return True
        else:
            logger.error(f"Failed to send message: {response.status_code} - {response.text}")
            return False
    
    except Exception as e:
        logger.error(f"Error sending AI response to chat: {e}")
        return False