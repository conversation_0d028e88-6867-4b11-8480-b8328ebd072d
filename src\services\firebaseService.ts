import * as admin from 'firebase-admin';
import { config } from '../config/env';
import { UserDeviceDetails } from '../models/user';
import pool from '../database/connection';

export interface FCMNotificationPayload {
    title: string;
    body: string;
    data?: { [key: string]: string };
}

export interface FCMMessage {
    token: string;
    notification: FCMNotificationPayload;
    data?: { [key: string]: string };
}

export class FirebaseService {
    private static instance: FirebaseService;
    private app: admin.app.App | null = null;

    private constructor() {
        this.initializeFirebase();
    }

    public static getInstance(): FirebaseService {
        if (!FirebaseService.instance) {
            FirebaseService.instance = new FirebaseService();
        }
        return FirebaseService.instance;
    }

    private initializeFirebase(): void {
        try {
            if (admin.apps.length === 0) {
                const serviceAccountPath = config.firebase.serviceAccountPath;
                
                if (!serviceAccountPath) {
                    console.warn('Firebase service account path not configured. FCM notifications will be disabled.');
                    return;
                }

                // Initialize Firebase Admin SDK
                this.app = admin.initializeApp({
                    credential: admin.credential.cert(serviceAccountPath),
                });

                console.log('Firebase Admin SDK initialized successfully');
            } else {
                this.app = admin.apps[0] as admin.app.App;
            }
        } catch (error) {
            console.error('Failed to initialize Firebase Admin SDK:', error);
            this.app = null;
        }
    }

    /**
     * Send FCM notification to a single device
     */
    public async sendNotification(message: FCMMessage): Promise<boolean> {
        if (!this.app) {
            console.warn('Firebase not initialized. Cannot send notification.');
            return false;
        }

        try {
            const messaging = admin.messaging(this.app);
            const response = await messaging.send({
                token: message.token,
                notification: {
                    title: message.notification.title,
                    body: message.notification.body,
                },
                data: message.data || {},
                android: {
                    notification: {
                        sound: 'default',
                        priority: 'high' as const,
                    },
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1,
                        },
                    },
                },
            });

            console.log('Successfully sent FCM message:', response);
            return true;
        } catch (error) {
            console.error('Error sending FCM message:', error);
            
            // Handle invalid token errors
            if (error instanceof Error && error.message.includes('registration-token-not-registered')) {
                console.log('Invalid FCM token detected, should remove from database');
                // TODO: Remove invalid token from database
            }
            
            return false;
        }
    }

    /**
     * Send FCM notifications to multiple devices
     */
    public async sendMulticastNotification(
        tokens: string[],
        notification: FCMNotificationPayload,
        data?: { [key: string]: string }
    ): Promise<{ successCount: number; failureCount: number }> {
        if (!this.app) {
            console.warn('Firebase not initialized. Cannot send notifications.');
            return { successCount: 0, failureCount: tokens.length };
        }

        if (tokens.length === 0) {
            return { successCount: 0, failureCount: 0 };
        }

        try {
            const messaging = admin.messaging(this.app);
            const response = await messaging.sendEachForMulticast({
                tokens,
                notification: {
                    title: notification.title,
                    body: notification.body,
                },
                data: data || {},
                android: {
                    notification: {
                        sound: 'default',
                        priority: 'high' as const,
                    },
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1,
                        },
                    },
                },
            });

            console.log(`FCM multicast result: ${response.successCount} successful, ${response.failureCount} failed`);
            
            // Handle failed tokens
            if (response.failureCount > 0) {
                response.responses.forEach((resp, idx) => {
                    if (!resp.success) {
                        console.error(`Failed to send to token ${tokens[idx]}:`, resp.error);
                    }
                });
            }

            return {
                successCount: response.successCount,
                failureCount: response.failureCount,
            };
        } catch (error) {
            console.error('Error sending multicast FCM message:', error);
            return { successCount: 0, failureCount: tokens.length };
        }
    }

    /**
     * Get FCM tokens for a user by user_reference
     */
    public async getUserFCMTokens(userReference: string): Promise<string[]> {
        const client = await pool.connect();
        try {
            const result = await client.query(
                'SELECT fcm_token FROM user_device_details WHERE user_reference = $1 AND fcm_token IS NOT NULL',
                [userReference]
            );
            
            return result.rows.map(row => row.fcm_token).filter(token => token && token.trim() !== '');
        } catch (error) {
            console.error('Error fetching FCM tokens:', error);
            return [];
        } finally {
            client.release();
        }
    }

    /**
     * Send notification to user by user_reference
     */
    public async sendNotificationToUser(
        userReference: string,
        notification: FCMNotificationPayload,
        data?: { [key: string]: string }
    ): Promise<boolean> {
        const tokens = await this.getUserFCMTokens(userReference);
        
        if (tokens.length === 0) {
            console.log(`No FCM tokens found for user: ${userReference}`);
            return false;
        }

        const result = await this.sendMulticastNotification(tokens, notification, data);
        return result.successCount > 0;
    }

    /**
     * Update or insert FCM token for a user
     */
    public async updateUserFCMToken(
        userReference: string,
        deviceId: string,
        fcmToken: string,
        appVersion?: string
    ): Promise<boolean> {
        const client = await pool.connect();
        try {
            await client.query('BEGIN');

            // Check if device details already exist
            const existingResult = await client.query(
                'SELECT user_device_details_id FROM user_device_details WHERE user_reference = $1 AND device_id = $2',
                [userReference, deviceId]
            );

            if (existingResult.rows.length > 0) {
                // Update existing record
                await client.query(
                    'UPDATE user_device_details SET fcm_token = $1, user_app_version = $2, updated_at = CURRENT_TIMESTAMP WHERE user_reference = $3 AND device_id = $4',
                    [fcmToken, appVersion, userReference, deviceId]
                );
            } else {
                // Insert new record
                await client.query(
                    'INSERT INTO user_device_details (user_reference, device_id, fcm_token, user_app_version) VALUES ($1, $2, $3, $4)',
                    [userReference, deviceId, fcmToken, appVersion]
                );
            }

            await client.query('COMMIT');
            console.log(`FCM token updated for user: ${userReference}, device: ${deviceId}`);
            return true;
        } catch (error) {
            await client.query('ROLLBACK');
            console.error('Error updating FCM token:', error);
            return false;
        } finally {
            client.release();
        }
    }

    /**
     * Remove FCM token for a user device
     */
    public async removeFCMToken(userReference: string, deviceId: string): Promise<boolean> {
        const client = await pool.connect();
        try {
            await client.query(
                'UPDATE user_device_details SET fcm_token = NULL, updated_at = CURRENT_TIMESTAMP WHERE user_reference = $1 AND device_id = $2',
                [userReference, deviceId]
            );
            
            console.log(`FCM token removed for user: ${userReference}, device: ${deviceId}`);
            return true;
        } catch (error) {
            console.error('Error removing FCM token:', error);
            return false;
        } finally {
            client.release();
        }
    }
}
