import express from 'express';
import { upload } from '../middleware/fileUpload';
import { handleFileUpload } from '../controllers/fileController';
import { authMiddleware } from '../middleware/auth';

const router = express.Router();

// Route for file uploads
router.post('/upload', 
    authMiddleware,
    upload.array('files', 4), // Maximum 4 files at once
    handleFileUpload
);

export default router;
