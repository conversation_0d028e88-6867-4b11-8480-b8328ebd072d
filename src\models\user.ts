export interface User {
    id: string;
    username: string;
    email: string;
    password: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
    created_at: Date;
    updated_at: Date;
    entity_type: string;
}

export interface UserCreateDTO {
    username: string;
    email: string;
    password: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
}

export interface UserUpdateDTO {
    username?: string;
    email?: string;
    password?: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
}

export interface UserResponse {
    id: string;
    username: string;
    email: string;
    created_at: Date;
    updated_at: Date;
    entity_type: string;
}

export interface UserDeviceDetails {
    user_device_details_id: number;
    user_reference: string;
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
    created_at?: Date;
    updated_at?: Date;
}

export interface UserDeviceDetailsCreateDTO {
    user_reference: string;
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
}

export interface UserDeviceDetailsUpdateDTO {
    device_id?: string;
    fcm_token?: string;
    user_app_version?: string;
}