export interface User {
    id: string;
    username: string;
    email: string;
    password: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
    created_at: Date;
    updated_at: Date;
    entity_type: string;
}

export interface UserCreateDTO {
    username: string;
    email: string;
    password: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
}

export interface UserUpdateDTO {
    username?: string;
    email?: string;
    password?: string;
    user_reference?: string;
    user_icon?: string;
    name?: string;
}

export interface UserResponse {
    id: string;
    username: string;
    email: string;
    created_at: Date;
    updated_at: Date;
    entity_type: string;
}