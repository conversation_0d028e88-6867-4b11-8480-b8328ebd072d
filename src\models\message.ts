export interface Message {
    id: string;
    chat_id: string;
    sender_id: string;
    content: string;
    sequence_number: number;
    message_type: MessageType;
    object?: string;
    metadata?: any;
    attachments?: any[];
    created_at: Date;
    updated_at: Date;
}

export enum MessageType {
    TEXT = 'text',
    IMAGE = 'image',
    FILE = 'file',
    SYSTEM = 'system'
}

export interface MessageCreateDTO {
    chat_id: string;
    content: string;
    message_type: MessageType;
    object?: string;
    metadata?: any;
    attachments?: any[];
}

export interface MessageQuery {
    chatId: string;
    beforeSequence?: number;
    afterSequence?: number;
    limit?: number;
}

export interface MessageResponse extends Message {
    sender_name: string;
}