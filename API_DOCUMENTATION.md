# Swadesic Messaging API Documentation

This document provides details about all available REST APIs and WebSocket messages in the Swadesic Messaging platform.

## Base URL
```
http://localhost:4000/api
```
## REST APIs


### Chats

#### Create New Chat
- Create a Chat with another messaging user - (USER or STORE in Swadesic)
- Chat is created when a new message is sent to a user or store
```bash
curl --location 'http://localhost:4000/api/chats/create_chat' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "chat_type": "GROUP",
    "chat_name": "Marketing Team Discussion",
    "message_access": "ADMIN_ONLY",
    "member_ids": ["b131ecba-0438-4be7-a01d-6ca439743ca2", "c242fdcb-1549-5ce8-b02e-7db550854db3"]
  }'
```

Response:
```json
{
    "message": "Chat created successfully",
    "chat": {
        "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
        "chat_type": "GROUP",
        "chat_name": "Marketing Team Discussion",
        "message_access": "ADMIN_ONLY",
        "created_at": "2025-03-12T11:06:02.621Z",
        "updated_at": "2025-03-12T11:06:02.621Z",
        "last_sequence_number": 0
    }
}
```


#### Get All Chats
- Get all chats of the user, to be called in chat home screen
- We will segment the chat list into active chats and requests based on is_subscribed status

```bash
curl --location 'http://localhost:4000/api/chats/get_all_chats' \
--header 'Authorization: Bearer <token>'
```

Response:
```json
[
    {
        "chat_id": "4916cb57-699d-4a8a-b086-c7bebdcf67f0",
        "chat_icon": null,
        "chat_name": "SAI_1721930951916",
        "chat_type": "DIRECT",
        "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "created_at": "2025-03-12T11:30:10.455Z",
        "updated_at": "2025-03-12T11:30:10.455Z",
        "last_read_sequence": 99,
        "unread_count": 100,
        "is_subscribed": true,
        "role_type": null,
        "is_muted": false,
        "entity_type": "STORE",
        "chat_preview": {
            "preview_text": "You: Hello there",
            "timestamp": "2025-03-12T11:30:10.455Z"
        }
    },
    {
        "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
        "chat_icon": "/uploads/new_profile_icon.jpg",
        "chat_name": "testuser",
        "chat_type": "DIRECT",
        "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "created_at": "2025-03-12T11:06:02.621Z",
        "updated_at": "2025-03-12T11:06:02.621Z",
        "last_read_sequence": 25,
        "unread_count": 26,
        "is_subscribed": false,
        "role_type": "MEMBER",
        "is_muted": false,
        "entity_type": "USER",
        "chat_preview": {
            "preview_text": "How are you?",
            "timestamp": "2025-03-12T11:06:02.621Z"
        }
    }
]
```

#### Get Chats V2 (Paginated with Recommendations)
- Get user chats based on subscription status with pagination and recommended contacts
- Supports filtering by ACTIVE (subscribed) or REQUEST (unsubscribed) chats
- Includes recommended contacts from Neo4j when available

```bash
curl --location 'http://localhost:4000/api/chats/get_chats/v2?chatType=ACTIVE&limit=10&offset=0' \
--header 'Authorization: Bearer <token>'
```

Parameters:
- `chatType`: "ACTIVE" or "REQUEST"
- `limit`: Number of items to return (default: 10)
- `offset`: Number of items to skip (default: 0)

Response:
```json
[
    {
        "chat_id": "4916cb57-699d-4a8a-b086-c7bebdcf67f0",
        "chat_icon": null,
        "chat_name": "SAI_1721930951916",
        "chat_type": "DIRECT",
        "created_at": "2025-03-12T11:30:10.455Z",
        "updated_at": "2025-03-12T11:30:10.455Z",
        "last_read_sequence": 99,
        "unread_count": 100,
        "is_subscribed": true,
        "role_type": null,
        "is_muted": false,
        "entity_type": "STORE",
        "connecting_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "chat_preview": {
            "preview_text": "You: Hello there",
            "timestamp": "2025-03-12T11:30:10.455Z"
        }
    },
    {
        "chat_id": "",
        "chat_icon": "/uploads/recommended_user.jpg",
        "chat_name": "recommended_user",
        "chat_type": "direct",
        "created_at": "",
        "updated_at": "",
        "last_read_sequence": 0,
        "unread_count": 0,
        "is_subscribed": false,
        "role_type": null,
        "is_muted": false,
        "entity_type": "USER",
        "connecting_id": "8b455a2b-fd29-4d68-921a-1c0adb084122",
        "chat_preview": null
    }
]
```

#### Get Chat by ID
- Get chat details by chat ID
```bash
curl --location 'http://**************:4000/api/chats/get_chat_by_chatId/chatId=<chat_id>' \
--header 'Authorization: Bearer <your_jwt_token>' \
--header 'Content-Type: application/json'
```
Response:
```json
{
    "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "chat_icon": "/uploads/new_profile_icon.jpg",
    "chat_name": "testuser",
    "chat_type": "DIRECT",
    "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
    "created_at": "2025-03-12T11:06:02.621Z",
    "updated_at": "2025-03-12T11:06:02.621Z",
    "last_read_sequence": 25,
    "unread_count": 26,
    "is_subscribed": false,
    "role_type": "MEMBER",
    "is_muted": false,
    "entity_type": "USER",
    "chat_preview": {
        "preview_text": "How are you?",
        "timestamp": "2025-03-12T11:06:02.621Z"
    }
}
```

#### Check Direct Chat
- Check if a direct chat exists between two users
```bash
curl --location 'http://localhost:4000/api/chats/check_direct_chat/userId=50aec3eb-4edb-4ccc-82ab-3ce49f211afc' \
--header 'Authorization: Bearer <token>'
```

Response:
200
```json
{
    "message": "Chat already exists",
    "chat": {
        "chat_id": "4916cb57-699d-4a8a-b086-c7bebdcf67f0",
        "chat_icon": null,
        "chat_name": "SAI_1721930951916",
        "chat_type": "DIRECT",
        "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "created_at": "2025-03-12T11:30:10.455Z",
        "updated_at": "2025-03-12T11:30:10.455Z",
        "last_read_sequence": 99,
        "unread_count": 100,
        "is_subscribed": true,
        "role_type": null,
        "is_muted": false,
        "entity_type": "STORE",
        "chat_preview": {
            "preview_text": "You: Hello there",
            "timestamp": "2025-03-12T11:30:10.455Z"
        }
    }
}
```

404
```json
{
  "message": "No chat found between these users."
}
```

500
```json
{
  "message": "Error fetching chat"
}
```


#### Chat Search
- Search for chats by name or username
```bash
curl --location 'http://localhost:4000/api/chats/search_chats/searchQuery=t/limit=100/offset=0' \
--header 'Authorization: Bearer <token>'
```
response:

success response
```json
[
    {
        "chat_id": "ae6a4123-9ba8-42ec-b2ca-e8f834047665",
        "chat_type": "direct",
        "chat_name": "testuser",
        "chat_icon": "/uploads/new_profile_icon.jpg",
        "user_id": "b131ecba-0438-4be7-a01d-6ca439743ca2",
        "entity_type": "USER",
        "last_read_sequence": 0,
        "unread_count": 0,
        "is_subscribed": false,
        "role_type": "MEMBER",
        "is_muted": false,
        "created_at": "2025-03-07T08:34:08.657Z",
        "updated_at": "2025-03-07T08:34:08.657Z",
        "connecting_id": null,
        "chat_preview": {
            "preview_text": "Hello there",
            "timestamp": "2025-03-07T08:34:08.657Z"
        }
    },
    {
        "chat_id": null,
        "chat_type": null,
        "chat_name": "testuser3",
        "chat_icon": null,
        "user_id": "8b455a2b-fd29-4d68-921a-1c0adb084122",
        "entity_type": "USER",
        "last_read_sequence": 0,
        "unread_count": 0,
        "is_subscribed": false,
        "role_type": null,
        "is_muted": false,
        "created_at": null,
        "updated_at": null,
        "connecting_id": "8b455a2b-fd29-4d68-921a-1c0adb084122",
        "chat_preview": null
    }
]
```

error response
```json
{
    "message": "Error searching chats"
}
```

#### Update Chat
- Update chat details such as name, type, or message access permissions
- Only chat members can update chat details
```bash
curl --location --request PUT 'http://localhost:4000/api/chats/update_chat/chatId-<chat_id>' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "chatName": "Updated Marketing Team",
    "chatType": "GROUP",
    "messageAccess": "MEMBERS_ONLY"
}'
```

Request Body (all fields are optional):
```json
{
    "chatName": "string",        // New name for the chat
    "chatType": "string",        // Chat type: "DIRECT" or "GROUP"
    "messageAccess": "string"    // Message access control: "ADMIN_ONLY", "MEMBERS_ONLY", or null
}
```

Response:
```json
{
    "id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "chat_type": "GROUP",
    "chat_name": "Updated Marketing Team",
    "message_access": "MEMBERS_ONLY",
    "created_at": "2025-03-12T11:06:02.621Z",
    "updated_at": "2025-03-12T15:30:45.123Z",
    "last_sequence_number": 15
}
```

#### Delete Chat
- Delete a chat and all its messages (only for chat members)
```bash
curl --location --request DELETE 'http://localhost:4000/api/chats/delete_chat/chatId=<chat_id>' \
--header 'Authorization: Bearer <token>'
```

Response:
```json
{
    "message": "Chat deleted successfully"
}
```

#### Add Members to Chat
- Add new members to an existing chat with optional role assignment
```bash
curl --location 'http://localhost:4000/api/chats/chatId=<chat_id>/members' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "memberIds": ["user_id_1", "user_id_2"],
    "role_type": "ADMIN"
  }'
```

**Request Parameters:**
- `memberIds` (required): Array of user IDs to add to the chat
- `role_type` (optional): Role to assign to new members (`MEMBER` or `ADMIN`)

**Examples:**

Add members with default role (null):
```json
{
    "memberIds": ["user_id_1", "user_id_2"]
}
```

Add members as ADMINs:
```json
{
    "memberIds": ["user_id_1", "user_id_2"],
    "role_type": "ADMIN"
}
```

Add members as MEMBERs:
```json
{
    "memberIds": ["user_id_1", "user_id_2"],
    "role_type": "MEMBER"
}
```

Response:
```json
{
    "message": "Members added successfully",
    "role_assigned": "ADMIN"
}
```

#### Remove Member from Chat
- Remove a member from a chat
```bash
curl --location --request DELETE 'http://localhost:4000/api/chats/chatId-<chat_id>/members/userId=<user_id>' \
--header 'Authorization: Bearer <token>'
```

Response:
```json
{
    "message": "Member removed successfully"
}
```

### File Upload

#### Upload Files
- Upload files to the server and get the file URLs & original file names
```bash
curl --location 'http://localhost:4000/api/files/upload' \
--header 'Authorization: Bearer <token>' \
--form 'files=@"/C:/Users/<USER>/Pictures/Screenshots/Screenshot 2025-02-15 012315.png"' \
--form 'files=@"/C:/Users/<USER>/Pictures/Screenshots/Screenshot 2025-02-15 002322.png"' \
--form 'files=@"/C:/Users/<USER>/Videos/Captures/Upload a file - Swadesic Messaging New 2025-03-12 17-55-05.mp4"'
```

Response:
```json
{
    "success": true,
    "message": "Files uploaded successfully",
    "data": [
        {
            "original_name": "Screenshot 2025-02-15 012315.png",
            "file_name": "95aa4e40-a6fa-42ac-8c06-3cb1bc435841.png",
            "file_type": "image",
            "file_size": 23496,
            "url": "/uploads/95aa4e40-a6fa-42ac-8c06-3cb1bc435841.png",
            "mimetype": "image/png",
            "sender_user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647"
        },
        {
            "original_name": "Screenshot 2025-02-15 002322.png",
            "file_name": "dd066378-4756-4857-9580-60ad4a48dd65.png",
            "file_type": "image",
            "file_size": 33318,
            "url": "/uploads/dd066378-4756-4857-9580-60ad4a48dd65.png",
            "mimetype": "image/png",
            "sender_user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647"
        },
        {
            "original_name": "Upload a file - Swadesic Messaging New 2025-03-12 17-55-05.mp4",
            "file_name": "518ca0cc-5474-4c7b-9c22-120d90a23dbc.mp4",
            "file_type": "document",
            "file_size": 9624224,
            "url": "/uploads/518ca0cc-5474-4c7b-9c22-120d90a23dbc.mp4",
            "mimetype": "video/mp4",
            "sender_user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647"
        }
    ]
}
```

Note: Maximum 4 files can be uploaded at once.

### Messages

#### Send Message
- Send a message to a chat
- Use this only in rare scenarios where we don't use websockets
```bash
curl --location 'http://localhost:4000/api/messages/send_message' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "content": "Hello, new message here",
    "message_type": "TEXT",
    "metadata": {},
    "attachments": ["<attachment data>"]
  }'
```

Response:
```json
{
    "message_id": "e29add14-d47d-4f67-93b1-f570fe5c9569",
    "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "sender_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
    "content": "Hello, new message here",
    "message_type": "TEXT",
    "metadata": {},
    "attachments": [
        "<attachment data>"
    ],
    "sequence_number": 4,
    "created_at": "2025-03-12T13:20:32.306Z",
    "updated_at": "2025-03-12T13:20:32.306Z"
}
```


#### Get Messages from sequence number
Api to get messages from a specific anchor sequence number in either PAST, FUTURE or BI-DIRECTION
```bash
curl --location 'http://localhost:4000/api/messages/get_messages_from_sequence?chat_id=3a01fe5c-d383-4271-9973-b7192c903dc7&anchor_sequence=15&direction=BI-DIRECTION&limit=4' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.7rldirovCXZu0-mXLSFulr-gs21zzqZfwqmPQcvUTUk'
```

Response:
```json
[
    {
        "message_id": "cb325cd3-f02c-4c84-8a0d-c50d5e97ddca",
        "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7",
        "sender_id": "2e742ddb-c092-4596-951d-482a66a63451",
        "content": "Check out these text messages!",
        "message_type": "text",
        "metadata": {},
        "attachments": [
            {
                "url": "/uploads/9ff32efd-f483-4048-a063-864e33a6db03.jpg",
                "size": 35394,
                "type": "image",
                "file_name": "9ff32efd-f483-4048-a063-864e33a6db03.jpg",
                "mime_type": "image/jpeg",
                "original_name": "IMG_20221010_203232.jpg"
            }
        ],
        "sequence_number": 14,
        "created_at": "2025-03-13T09:19:28.555Z",
        "updated_at": "2025-03-13T09:19:28.555Z",
        "sender_name": "krishna_k"
    },
    {
        "message_id": "1fcb41bc-cac4-4520-b6ce-d9e764741268",
        "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7",
        "sender_id": "2e742ddb-c092-4596-951d-482a66a63451",
        "content": "Check out these text messages!",
        "message_type": "text",
        "metadata": {},
        "attachments": [
            {
                "url": "/uploads/9ff32efd-f483-4048-a063-864e33a6db03.jpg",
                "size": 35394,
                "type": "image",
                "file_name": "9ff32efd-f483-4048-a063-864e33a6db03.jpg",
                "mime_type": "image/jpeg",
                "original_name": "IMG_20221010_203232.jpg"
            }
        ],
        "sequence_number": 15,
        "created_at": "2025-03-13T10:30:58.494Z",
        "updated_at": "2025-03-13T10:30:58.494Z",
        "sender_name": "krishna_k"
    }
]
```

#### Get Message by ID
- Get a message by its ID
```bash
curl --location 'http://localhost:4000/api/messages/get_message_by_messsageId/messageId=99eed08e-767d-46dc-882b-1c819ec93d99' \
--header 'Authorization: Bearer <token>'
```
Response:
```json
{
    "message_id": "99eed08e-767d-46dc-882b-1c819ec93d99",
    "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "sender_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
    "content": "Hello, new message here",
    "message_type": "TEXT",
    "metadata": {},
    "attachments": [],
    "sequence_number": 1,
    "created_at": "2025-03-12T13:14:34.230Z",
    "updated_at": "2025-03-12T13:14:34.230Z",
    "sender_name": "new_username146"
}
```

#### Delete Message
- Delete a message by its ID by the sender
```bash
curl --location --request DELETE 'http://localhost:4000/api/messages/delete_message/messageId=99eed08e-767d-46dc-882b-1c819ec93d99' \
--header 'Authorization: Bearer <token>'
```

Response:
```json
{
    "message": "Message deleted successfully"
}
```

#### Mark Messages as Read
- Mark messages as read by the receiver
- All messages before the last read sequence will be marked as read
```bash
curl --location 'http://**************:4000/api/messages/mark_as_read' \
--header 'Authorization: Bearer <your_jwt_token>' \
--header 'Content-Type: application/json' \
--data '{
    "chat_id": "b3e31cc7-5e93-4916-966b-57213e68e3fa",
    "last_read_sequence":19
  }'
```

Response:
```json
{
    "message": "Messages marked as read"
}
```


## WebSocket APIs

### Connection URL
To connect to the WebSocket server, use the following URL:
```
ws://localhost:<WS_PORT>?token=<your_jwt_token>
```


### Message Operations

#### Send New Message
- Send a new message to a chat
- the message is broadcasted to all chat subscribers
```json
// Request
{
    "type":"new_message", 
    "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7",
    "content": "Check out these text messages!", 
    "message_type": "TEXT",
    "metadata":{},
    "attachments": [
        "<attachment data>"
    ]
}


// Response (broadcast to all chat subscribers)
{
    "type": "new_message",
    "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7",
    "message": {
        "message_id":"cb325cd3-f02c-4c84-8a0d-c50d5e97ddca",
        "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7",
        "sender_id": "2e742ddb-c092-4596-951d-482a66a63451",
        "content": "Check out these text messages!",
        "message_type": "TEXT",
        "metadata": {},
        "attachments": [
            "<attachment data>"
        ],
        "sequence_number": 14,
        "created_at": "2025-03-13T09:19:28.555Z",
        "updated_at": "2025-03-13T09:19:28.555Z"
    }
}

```

### Chat Subscription

#### Subscribe to Chat
- Subscribe to a chat to receive real-time updates
- we subscribe when a message is sent
```javascript
// Request
{
    "type": "subscribe_chat",
    "chat_id": "a43a8bb3-e98d-415b-8baf-615aff315aa1"
}
// Response
{
    "type": "subscribed",
    "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7"
}
```

#### Unsubscribe from Chat
- Unsubscribe from a chat to stop receiving real-time updates
```javascript
// Request
{
  "type": "unsubscribe_chat",
  "chat_id": "3a01fe5c-d383-4271-9973-b7192c903dc7"
}

// Response
{
    "type": "unsubscribed",
    "chat_id": "a43a8bb3-e98d-415b-8baf-615aff315aa1"
}
```




#### Mark messages as read
- Mark messages as read by the receiver
- All messages before the last read sequence will be marked as read
```json
// Request
{
    "type":"mark_messages_as_read",
    "chat_id":"b3e31cc7-5e93-4916-966b-57213e68e3fa",
    "last_read_sequence":25
}
// Response
{
    "type": "marked_messages_as_read",
    "chat_id": "b3e31cc7-5e93-4916-966b-57213e68e3fa",
    "user_id": "a43a8bb3-e98d-415b-8baf-615aff315aa1",
    "last_read_sequence": 25
}
```


#### Chat updates
- Chat updates are sent to all subscribers of a chat
- It is similar to Get all chats response but only contains chats that have an update
```json
Response

{
    "type": "chat_update",
    "chats": [
    {
        "chat_id": "4916cb57-699d-4a8a-b086-c7bebdcf67f0",
        "chat_icon": null,
        "chat_name": "SAI_1721930951916",
        "chat_type": "DIRECT",
        "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "created_at": "2025-03-12T11:30:10.455Z",
        "updated_at": "2025-03-12T11:30:10.455Z",
        "last_read_sequence": 99,
        "unread_count": 100,
        "is_subscribed": true,
        "role_type": null,
        "is_muted": false,
        "entity_type": "STORE",
        "chat_preview": {
            "preview_text": "You: Hello there",
            "timestamp": "2025-03-12T11:30:10.455Z"
        }
    },
    {
        "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
        "chat_icon": "/uploads/new_profile_icon.jpg",
        "chat_name": "testuser",
        "chat_type": "DIRECT",
        "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
        "created_at": "2025-03-12T11:06:02.621Z",
        "updated_at": "2025-03-12T11:06:02.621Z",
        "last_read_sequence": 25,
        "unread_count": 26,
        "is_subscribed": false,
        "role_type": "MEMBER",
        "is_muted": false,
        "entity_type": "USER",
        "chat_preview": {
            "preview_text": "How are you?",
            "timestamp": "2025-03-12T11:06:02.621Z"
        }
    }
    ]
}
```
#### Types of websocket messages for processing: 
Receiving types: 
'welcome' - acknowledgment for connection
'chat_update' - chat updates
'subscribed' - acknowledgment for subscription
'unsubscribed' - acknowledgment for unsubscription
'new_message' - new message received
'marked_messages_as_read' - acknowledgment to know that messages have been marked as read

Sending types:
'new_message' - to send a new message
'subscribe_chat' - to subscribe to a chat
'unsubscribe_chat' - to unsubscribe from a chat
'mark_messages_as_read' - to send a request to mark messages as read

## New Chat Management Features

### Update Member Role
- Update a member's role in a chat (ADMIN or MEMBER)
```bash
curl --location --request PUT 'http://localhost:4000/api/chats/chatId=<chat_id>/members/userId=<user_id>/role' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "role_type": "ADMIN"
  }'
```

Response:
```json
{
    "message": "Member role updated successfully"
}
```

### Update Mute Status
- Mute or unmute notifications for a specific chat
```bash
curl --location --request PUT 'http://localhost:4000/api/chats/chatId=<chat_id>/mute' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "is_muted": true
  }'
```

Response:
```json
{
    "message": "Mute status updated successfully"
}
```

### Get Admin Group Chat IDs
- Get all group chat IDs where the current user is an admin
```bash
curl --location --request GET 'http://localhost:4000/api/chats/admin-groups' \
--header 'Authorization: Bearer <token>'
```

Response:
```json
{
    "message": "Admin group chat IDs retrieved successfully",
    "count": 3,
    "data": [
        "2860458e-7a52-4495-b4e9-06fbe4f18029",
        "3971569f-8b63-5506-c5fa-17gce5f29140",
        "4a82670g-9c74-6617-d6gb-28hdf6g30251"
    ]
}
```

### Enhanced Chat Response Fields
All chat-related API responses now include the following additional fields:

- `chat_name`: Custom name for the chat (especially useful for group chats)
- `role_type`: User's role in the chat (`MEMBER`, `ADMIN`, or `null`)
- `is_muted`: Whether the user has muted notifications for this chat (`true`/`false`)
- `chat_preview`: Object containing the latest message preview with `preview_text` and `timestamp`

### Chat Icon Behavior
- **For DIRECT chats**: Shows the other user's profile icon
- **For GROUP chats**: Shows the chat admin's profile icon (user with `role_type = 'ADMIN'`)

### Complete Enhanced Response Example:
```json
{
    "chat_id": "2860458e-7a52-4495-b4e9-06fbe4f18029",
    "chat_icon": "/uploads/admin_profile_icon.jpg",
    "chat_name": "Marketing Team Discussion",
    "chat_type": "GROUP",
    "message_access": "ADMIN_ONLY",
    "user_id": "265ac658-041c-4d53-adf3-c9a10cc6b647",
    "created_at": "2025-03-12T11:06:02.621Z",
    "updated_at": "2025-03-12T11:06:02.621Z",
    "last_read_sequence": 25,
    "unread_count": 26,
    "is_subscribed": false,
    "role_type": "ADMIN",
    "is_muted": false,
    "entity_type": "USER",
    "chat_preview": {
        "preview_text": "Let's discuss the new campaign",
        "timestamp": "2025-03-12T11:06:02.621Z"
    }
}

## Message Access Control

### Overview
The `message_access` field controls who can send messages in group chats:

- `ADMIN_ONLY`: Only users with `role_type = 'ADMIN'` can send messages
- `MEMBERS_ONLY`: Only users with `role_type = 'MEMBER'` or `'ADMIN'` can send messages
- `ALL`: All chat participants can send messages (including users with no specific role)
- `null`: No restrictions (default behavior for existing chats)

### Creating Chats with Message Access Control
```bash
curl --location --request POST 'http://localhost:4000/api/chats/create_chat' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "chat_type": "GROUP",
    "chat_name": "Store Announcements",
    "message_access": "ADMIN_ONLY",
    "member_ids": ["user1", "user2", "user3"]
  }'
```

### Updating Message Access
```bash
curl --location --request PUT 'http://localhost:4000/api/chats/update_chat/chatId-<chat_id>' \
--header 'Authorization: Bearer <token>' \
--header 'Content-Type: application/json' \
--data '{
    "messageAccess": "MEMBERS_ONLY"
  }'
```

### Use Cases

#### Store Announcements (`ADMIN_ONLY`)
- Only store owners can post announcements
- Customers can read but not reply
- Perfect for updates, promotions, news

#### Member Discussions (`MEMBERS_ONLY`)
- Only registered members can participate
- Excludes guests or unverified users
- Exclusive member conversations

#### Open Chats (`ALL`)
- Everyone can participate
- Public discussions
- Customer support chats

#### Default Behavior (`null`)
- No messaging restrictions
- Backward compatible with existing chats
- Standard group chat functionality

