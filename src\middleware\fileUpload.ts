import multer from 'multer';
import path from 'path';
import fs from 'fs';
import fileUploadConfig from '../config/fileUpload';

// Ensure upload directory exists
const uploadDir = path.join(process.cwd(), fileUploadConfig.uploadDir);
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

// File filter function
const fileFilter = (req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    try {
        // Check if it's an image
        const isImage = file.mimetype && file.mimetype.startsWith('image/');
        
        // For images, check against allowed types
        if (isImage && !fileUploadConfig.image.allowedTypes.includes(file.mimetype)) {
            return cb(null, false);
        }

        // For images, check count limit
        if (isImage && req.files) {
            const imageFiles = (req.files as Express.Multer.File[]).filter(f => 
                f && f.mimetype && f.mimetype.startsWith('image/')
            );
            if (imageFiles.length >= fileUploadConfig.image.maxFiles) {
                return cb(null, false);
            }
        }

        // Accept the file
        cb(null, true);
    } catch (error) {
        cb(null, false);
    }
};

// Configure multer upload
export const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: fileUploadConfig.document.maxSizeMB * 1024 * 1024
    }
});
