export interface Chat {
    id: string;
    chat_type: ChatType;
    chat_name?: string;
    message_access?: MessageAccess;
    created_at: Date;
    updated_at: Date;
}

export enum ChatType {
    DIRECT = 'direct',
    GROUP = 'group'
}

export enum RoleType {
    MEMBER = 'MEMBER',
    ADMIN = 'ADMIN'
}

export enum MessageAccess {
    ADMIN_ONLY = 'ADMIN_ONLY',
    MEMBERS_ONLY = 'MEMBERS_ONLY',
    ALL = 'ALL'
}

export interface ChatMember {
    chat_id: string;
    user_id: string;
    last_read_sequence: number;
    joined_at: Date;
    is_subscribed: boolean;
    role_type?: RoleType;
    is_muted: boolean;
}

export interface ChatCreateDTO {
    chat_type: ChatType;
    chat_name?: string;
    message_access?: MessageAccess;
    member_ids: string[];
}

export interface Message {
    id: string;
    content: string;
    senderId: string;
    timestamp: Date;
}

export interface ChatResponse extends Chat {
    members: ChatMemberResponse[];
    last_message?: Message;
    unread_count: number;
}

export interface ChatMemberResponse {
    user_id: string;
    username: string;
    last_read_sequence: number;
    joined_at: Date;
    is_subscribed: boolean;
    role_type?: RoleType;
    is_muted: boolean;
}