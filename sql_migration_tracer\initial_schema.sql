CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users Table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_reference VARCHAR(255),
    user_icon VARCHAR(255),
    name VARCHAR(255)
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- User Device Details Table
CREATE TABLE user_device_details (
    user_device_details_id SERIAL PRIMARY KEY,
    user_reference VARCHAR(255) NOT NULL REFERENCES users(user_reference) ON DELETE CASCADE,
    device_id VARCHAR(30),
    fcm_token VARCHAR(300),
    user_app_version VARCHAR(10),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_device_details_user_reference ON user_device_details(user_reference);
CREATE INDEX idx_user_device_details_fcm_token ON user_device_details(fcm_token);
CREATE INDEX idx_user_device_details_device_id ON user_device_details(device_id);

-- Create ENUM type for message_access
CREATE TYPE message_access_enum AS ENUM ('ADMIN_ONLY', 'MEMBERS_ONLY', 'ALL');

-- Chats Table
CREATE TABLE chats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chat_type VARCHAR(20) NOT NULL,
    chat_name VARCHAR(255),
    message_access message_access_enum DEFAULT NULL,
    total_sequence INT DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_sequence_number BIGINT DEFAULT 0
);

CREATE INDEX idx_chats_chat_type ON chats(chat_type);
CREATE INDEX idx_chats_chat_name ON chats(chat_name);
CREATE INDEX idx_chats_message_access ON chats(message_access);

-- Messages Table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chat_id UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    sequence_number BIGINT NOT NULL,
    message_type VARCHAR(20) NOT NULL,
    object VARCHAR(255),
    metadata JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    attachments JSONB
);

CREATE INDEX idx_messages_chat_id ON messages(chat_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- Create ENUM type for role_type
CREATE TYPE role_type_enum AS ENUM ('MEMBER', 'ADMIN');

-- Chat Members Table
CREATE TABLE chat_members (
    chat_id UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    last_read_sequence INT DEFAULT 0,
    joined_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_subscribed BOOLEAN NOT NULL DEFAULT FALSE,
    unread_count INT NOT NULL DEFAULT 0,
    entity_type VARCHAR(10),
    role_type role_type_enum DEFAULT NULL,
    is_muted BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (chat_id, user_id)
);

CREATE INDEX idx_chat_members_user_id ON chat_members(user_id);
CREATE INDEX idx_chat_members_chat_id ON chat_members(chat_id);
CREATE INDEX idx_chat_members_role_type ON chat_members(role_type);
CREATE INDEX idx_chat_members_is_muted ON chat_members(is_muted);

-- AI Queue Messages Table

CREATE TABLE ai_queue_messages (
    id SERIAL PRIMARY KEY,                    
    user_id UUID NOT NULL,                    
    sai_user_id UUID NOT NULL,                
    chat_id UUID NOT NULL,                    
    message TEXT NOT NULL,                    
    sai_reference VARCHAR(255),               
    user_reference VARCHAR(255),              
    sequence_number INT NOT NULL,             
    status VARCHAR(20) DEFAULT 'pending',     
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP 
);
