export const config = {
    database: {
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT!),
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME
    },
    server: {
        port: parseInt(process.env.PORT!),
        wsPort: parseInt(process.env.WS_PORT!)
    },
    jwt: {
        secret: process.env.JWT_SECRET!,
        expiresIn: '7d'
    }
};

export interface EnvironmentConfig {
    NODE_ENV: string;
    PORT: number;
    DB_HOST: string;
    DB_PORT: number;
    DB_NAME: string;
    DB_USER: string;
    DB_PASSWORD: string;
    JWT_SECRET: string;
    JWT_EXPIRY: string;
    REDIS_URL?: string;
    WS_PORT: number;
    FILE_UPLOAD_SIZE_LIMIT: number;
    ALLOWED_ORIGINS: string;
    AWS_ACCESS_KEY_ID: string;
    AWS_SECRET_ACCESS_KEY: string;
    AWS_REGION: string;
    AWS_S3_BUCKET: string;
}

export const validateEnv = (): EnvironmentConfig => {
    const requiredEnvVars = [
        'NODE_ENV',
        'PORT',
        'DB_HOST',
        'DB_PORT',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD',
        'JWT_SECRET',
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY',
        'AWS_REGION',
        'AWS_S3_BUCKET'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
        throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    return {
        NODE_ENV: process.env.NODE_ENV!,
        PORT: parseInt(process.env.PORT!),
        DB_HOST: process.env.DB_HOST!,
        DB_PORT: parseInt(process.env.DB_PORT!),
        DB_NAME: process.env.DB_NAME!,
        DB_USER: process.env.DB_USER!,
        DB_PASSWORD: process.env.DB_PASSWORD!,
        JWT_SECRET: process.env.JWT_SECRET!,
        JWT_EXPIRY: process.env.JWT_EXPIRY!,
        REDIS_URL: process.env.REDIS_URL,
        WS_PORT: parseInt(process.env.WS_PORT!),
        FILE_UPLOAD_SIZE_LIMIT: parseInt(process.env.FILE_UPLOAD_SIZE_LIMIT!),
        ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS!,
        AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID!,
        AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY!,
        AWS_REGION: process.env.AWS_REGION!,
        AWS_S3_BUCKET: process.env.AWS_S3_BUCKET!
    };
};