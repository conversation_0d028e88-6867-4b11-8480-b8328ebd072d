# Chat Backend API Documentation

## Overview
This is the API documentation for the Chat Backend application. This application provides functionality for user registration, chat management, and message handling.

## API Endpoints

### Register a User
- **Endpoint**: `/register`
- **Method**: `POST`
- **Description**: Registers a new user.

#### Request Body
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

#### Response
- **Success (201)**:
```json
{
  "message": "User registered successfully",
  "userId": "string"
}
```
- **Error (400)**:
```json
{
  "error": "Error message"
}
```

### Login a User
- **Endpoint**: `/login`
- **Method**: `POST`
- **Description**: Logs in a user and returns a token.

#### Request Body
```json
{
  "email": "string",
  "password": "string"
}
```

#### Response
- **Success (200)**:
```json
{
  "token": "string"
}
```
- **Error (401)**:
```json
{
  "error": "Invalid credentials"
}
```

## Usage
1. Start the server using `npm run dev`.
2. Use tools like <PERSON>man or curl to test the API endpoints.

## License
This project is licensed under the MIT License.
