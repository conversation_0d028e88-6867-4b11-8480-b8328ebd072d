openapi: 3.0.0
info:
  title: Chat Backend API
  version: 1.0.0
  description: API documentation for the Chat Backend application.

paths:
  /register:
    post:
      summary: Registers a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                email:
                  type: string
                  format: email
                password:
                  type: string
              required:
                - username
                - email
                - password
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  userId:
                    type: string
        '400':
          description: Error message
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string

  /login:
    post:
      summary: Logs in a user and returns a token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
              required:
                - email
                - password
      responses:
        '200':
          description: Successful login
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
