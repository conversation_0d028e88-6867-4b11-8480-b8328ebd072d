import { Router } from 'express';
import { UserController } from '../controllers/userController';

const router = Router();
const userController = new UserController();

// router.get('/get_all_users', (req, res, next) => {
    
//     if (req.query.userId) {
//         userController.getUser(req, res);
//     } else {
//         userController.getUsers(req, res);
//     }
// });

// router.get('/get_user/userId=:userId', (req, res) => {
//     userController.getUser(req, res);
// });

router.put('/update_user/userId=:userId', userController.updateUser);
router.delete('/delete_user/userId=:userId', userController.deleteUser);

export default router;