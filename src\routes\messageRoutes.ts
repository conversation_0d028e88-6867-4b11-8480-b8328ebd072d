import { Router } from 'express';
import { MessageController } from '../controllers/messageController';

const router = Router();
const messageController = new MessageController();

router.get('/get_messages_in_chat', messageController.getMessages);
router.get('/get_message_by_messsageId/messageId=:messageId', messageController.getMessage);
router.post('/send_message', messageController.createMessage);
router.delete('/delete_message/messageId=:messageId', messageController.deleteMessage);
router.post('/mark_as_read', messageController.markAsRead);
router.get('/get_messages_from_sequence', messageController.getMessagesFromSequence);

export default router;