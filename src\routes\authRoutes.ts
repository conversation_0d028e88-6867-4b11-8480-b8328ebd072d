import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { AuthService } from '../services/authService';
import  pool  from '../config/database';
import { authMiddleware } from '../middleware/auth';

const router = Router();
const authService = new AuthService(pool);
const authController = new AuthController(authService);

router.post('/login', authController.login);
router.post('/register', authController.register);
router.post('/refresh-token', authController.refreshToken);
router.put('/update_profile', authMiddleware, authController.updateUser);

export default router;