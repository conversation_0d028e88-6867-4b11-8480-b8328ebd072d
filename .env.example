DB_HOST=localhost
DB_PORT=5432
DB_NAME=swadesic_db
DB_USER=your_username
DB_PASSWORD=your_password
PORT=4000
WS_PORT=4040
JWT_SECRET=your-secret-key
JWT_EXPIRY=7d

# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_URL=redis://localhost:6379

# File Upload Configuration
FILE_UPLOAD_SIZE_LIMIT=********
ALLOWED_ORIGINS=http://localhost:3000

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket

# RabbitMQ Configuration (optional)
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_QUEUE=ai_messages_local
RABBITMQ_EXCHANGE=ai_messages_local
RABBITMQ_KEY=ai_messages_local

# Neo4j Configuration (optional)
NEO4J_HOST=localhost
NEO4J_PORT=7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j
