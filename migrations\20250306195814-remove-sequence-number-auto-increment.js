'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('messages', 'sequence_number', {
      type: Sequelize.BIGINT,
      allowNull: false,
      autoIncrement: false
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('messages', 'sequence_number', {
      type: Sequelize.BIGINT,
      allowNull: false,
      autoIncrement: true
    });
  }
};
