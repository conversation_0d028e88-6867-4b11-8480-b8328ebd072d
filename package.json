{"name": "chat-backend", "version": "1.0.0", "description": "Real-time chat application backend", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "debug": "cross-env NODE_OPTIONS=--inspect nodemon src/app.ts", "build": "tsc", "test": "jest", "migrate": "node-pg-migrate", "lint": "eslint . --ext .ts"}, "dependencies": {"@types/fluent-ffmpeg": "^2.1.27", "@types/multer": "^1.4.12", "aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "bull": "^4.16.5", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.0.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "neo4j-driver": "^5.28.1", "pg": "^8.13.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "sharp": "^0.33.5", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "ws": "^8.12.0", "xss": "^1.0.15", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/bcryptjs": "^2.4.2", "@types/bull": "^3.15.9", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/jest": "^29.4.0", "@types/joi": "^17.2.2", "@types/jsonwebtoken": "^9.0.1", "@types/node": "^18.19.76", "@types/pg": "^8.6.6", "@types/sharp": "^0.31.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "amqplib": "^0.10.5", "eslint": "^8.33.0", "jest": "^29.4.2", "node-pg-migrate": "^6.2.2", "nodemon": "^3.1.9", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}