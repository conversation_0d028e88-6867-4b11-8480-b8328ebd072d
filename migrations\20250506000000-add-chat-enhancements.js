'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add chat_name column to chats table
    await queryInterface.addColumn('chats', 'chat_name', {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: 'Name of the chat, especially useful for group chats'
    });

    // Add role_type column to chat_members table
    await queryInterface.addColumn('chat_members', 'role_type', {
      type: Sequelize.ENUM('MEMBER', 'ADMIN'),
      allowNull: true,
      defaultValue: null,
      comment: 'Role of the user in the chat - MEMBER or ADMIN'
    });

    // Add is_muted column to chat_members table
    await queryInterface.addColumn('chat_members', 'is_muted', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether the user has muted notifications for this chat'
    });

    // Create index on chat_name for better search performance
    await queryInterface.addIndex('chats', ['chat_name'], {
      name: 'idx_chats_chat_name'
    });

    // Create index on role_type for better query performance
    await queryInterface.addIndex('chat_members', ['role_type'], {
      name: 'idx_chat_members_role_type'
    });

    // Create index on is_muted for better query performance
    await queryInterface.addIndex('chat_members', ['is_muted'], {
      name: 'idx_chat_members_is_muted'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex('chats', 'idx_chats_chat_name');
    await queryInterface.removeIndex('chat_members', 'idx_chat_members_role_type');
    await queryInterface.removeIndex('chat_members', 'idx_chat_members_is_muted');

    // Remove columns
    await queryInterface.removeColumn('chats', 'chat_name');
    await queryInterface.removeColumn('chat_members', 'role_type');
    await queryInterface.removeColumn('chat_members', 'is_muted');

    // Drop the ENUM type (Sequelize will handle this automatically when removing the column)
  }
};
