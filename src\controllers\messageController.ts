import { Request, Response } from 'express';
import { MessageService } from '../services/messageService';

export class MessageController {
    private messageService: MessageService;

    constructor() {
        this.messageService = new MessageService();
    }

    getMessages = async (req: Request, res: Response) => {
        try {
            const { chat_id, before_sequence, after_sequence, limit } = req.query;
            console.log('Query parameters:', { chat_id, before_sequence, after_sequence, limit });
            const messages = await this.messageService.getMessages({
                chat_id: chat_id as string,
                before_sequence: before_sequence ? parseInt(before_sequence as string) : undefined,
                after_sequence: after_sequence ? parseInt(after_sequence as string) : undefined,
                limit: limit ? parseInt(limit as string) : 20
            });
            res.json(messages);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    getMessage = async (req: Request, res: Response) => {
        try {
            const { messageId } = req.params;
            const message = await this.messageService.getMessage(messageId);
            if (!message) {
                return res.status(404).json({ message: 'Message not found' });
            }
            res.json(message);
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    createMessage = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.id) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chat_id, content, message_type, metadata, attachments, object } = req.body;
            const message = await this.messageService.createMessage({
                chat_id: chat_id,
                sender_id: req.userData.id,
                content,
                message_type: message_type,
                metadata,
                attachments,
                object
            });
            res.status(201).json(message);
        } catch (error: any) {
            console.log(error);
            res.status(500).json({ message: error.message });
        }
    };

    deleteMessage = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.id) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { messageId } = req.params;
            await this.messageService.deleteMessage(messageId, req.userData.id);
            res.status(200).json({ message: 'Message deleted successfully' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    markAsRead = async (req: Request, res: Response) => {
        try {
            if (!req.userData || !req.userData.id) {
                return res.status(401).json({ message: 'Unauthorized' });
            }
            const { chat_id, last_read_sequence } = req.body;
            await this.messageService.markAsRead(
                chat_id,
                req.userData.id,
                last_read_sequence
            );
            res.status(200).json({ message: 'Messages marked as read' });
        } catch (error: any) {
            res.status(500).json({ message: error.message });
        }
    };

    getMessagesFromSequence = async (req: Request, res: Response) => {
        try {
            const { chat_id, anchor_sequence, direction, limit = 20 } = req.query;

            if (!chat_id || !anchor_sequence || !direction) {
                return res.status(400).json({ error: 'Missing required parameters' });
            }

            if (!['PAST', 'FUTURE', 'BI-DIRECTION'].includes(direction as string)) {
                return res.status(400).json({ error: 'Invalid direction parameter' });
            }

            const messages = await this.messageService.getMessagesFromSequence({
                chat_id: chat_id as string,
                anchor_sequence: Number(anchor_sequence),
                direction: direction as 'PAST' | 'FUTURE' | 'BI-DIRECTION',
                limit: Number(limit)
            });

            res.json(messages);
        } catch (error) {
            console.error('Error in getMessagesFromSequence:', error);
            res.status(500).json({ error: 'Internal server error' });
        }
    }
}