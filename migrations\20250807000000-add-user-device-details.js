'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create user_device_details table
    await queryInterface.createTable('user_device_details', {
      user_device_details_id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      user_reference: {
        type: Sequelize.STRING(255),
        allowNull: false,
        references: {
          model: 'users',
          key: 'user_reference'
        },
        onDelete: 'CASCADE'
      },
      device_id: {
        type: Sequelize.STRING(30),
        allowNull: true
      },
      fcm_token: {
        type: Sequelize.STRING(300),
        allowNull: true
      },
      user_app_version: {
        type: Sequelize.STRING(10),
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create indexes for better performance
    await queryInterface.addIndex('user_device_details', ['user_reference'], {
      name: 'idx_user_device_details_user_reference'
    });

    await queryInterface.addIndex('user_device_details', ['fcm_token'], {
      name: 'idx_user_device_details_fcm_token'
    });

    await queryInterface.addIndex('user_device_details', ['device_id'], {
      name: 'idx_user_device_details_device_id'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_device_details');
  }
};
