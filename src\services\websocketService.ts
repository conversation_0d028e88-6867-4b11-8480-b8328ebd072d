import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { Duplex } from 'stream';
import { verifyToken } from '../utils/auth';
import { MessageService } from './messageService';
import { Pool } from 'pg';
import { config } from '../config/env';

interface WebSocketClient extends WebSocket {
    userId?: string;
    chatIds?: Set<string>;
}

interface WebSocketMessage {
    type: string;
    [key: string]: any;
}

export class WebSocketService {
    private static instance: WebSocketService;
    private wss: WebSocketServer;
    private clients: Set<WebSocketClient>;
    private messageService?: MessageService;
    private pool: Pool;

    constructor() {
        this.wss = new WebSocketServer({ noServer: true });
        this.clients = new Set();
        this.pool = new Pool(config.database);
        this.initialize();
    }

    public static getInstance(): WebSocketService {
        if (!WebSocketService.instance) {
            WebSocketService.instance = new WebSocketService();
        }
        return WebSocketService.instance;
    }

    private getMessageService(): MessageService {
        if (!this.messageService) {
            this.messageService = new MessageService();
        }
        return this.messageService;
    }

    private initialize(): void {
        this.wss.on('connection', (ws: WebSocketClient) => {
            console.log('New client connected');
        });
    }

    public handleUpgrade(request: IncomingMessage, socket: Duplex, head: Buffer): void {
        try {
            if (!request.url) {
                console.error('No URL in request');
                socket.destroy();
                return;
            }

            // Verify token from query parameters
            const url = new URL(request.url, `http://${request.headers.host || 'localhost'}`);
            const token = url.searchParams.get('token');

            if (!token) {
                console.error('No token provided');
                socket.destroy();
                return;
            }

            verifyToken(token).then(decoded => {
                this.wss.handleUpgrade(request, socket, head, (ws) => {
                    this.wss.emit('connection', ws, request);
                    (ws as WebSocketClient).userId = decoded.id;
                    (ws as WebSocketClient).chatIds = new Set();
                    this.handleConnection(ws);
                });
            }).catch(error => {
                console.error('Error during token verification:', error);
                socket.destroy();
            });
        } catch (error) {
            console.error('Error during WebSocket upgrade:', error);
            socket.destroy();
        }
    }

    private async updateSubscriptionStatus(userId: string, chatId: string, isSubscribed: boolean): Promise<void> {
        try {
            await this.pool.query(
                `UPDATE chat_members 
                SET is_subscribed = $1
                WHERE user_id = $2 AND chat_id = $3`,
                [isSubscribed, userId, chatId]
            );
        } catch (error) {
            console.error('Error updating subscription status:', error);
            throw error;
        }
    }

    private async handleConnection(ws: WebSocketClient): Promise<void> {
        this.clients.add(ws);

        // Fetch subscribed chat IDs for the current user
        if (ws.userId) {
            try {
                const result = await this.pool.query(
                    `SELECT chat_id 
                    FROM chat_members 
                    WHERE user_id = $1 AND is_subscribed = true`,
                    [ws.userId]
                );

                // Initialize chatIds with subscribed chat IDs
                ws.chatIds = new Set(result.rows.map(row => row.chat_id));
                console.log(`Loaded ${ws.chatIds.size} subscribed chats for user ${ws.userId}`);
            } catch (error) {
                console.error('Error fetching subscribed chats:', error);
                // Fallback to empty set if query fails
                ws.chatIds = new Set();
            }
        }

        ws.on('message', (message: string) => {
            this.handleMessage(ws, message);
        });

        ws.on('close', () => {
            console.log('Client disconnected');
            this.clients.delete(ws);
        });

        // Send welcome message with subscribed chats
        ws.send(JSON.stringify({
            type: 'welcome',
            message: `Connected to WebSocket server as ${ws.userId}, ${ws.chatIds}`
        }));
    }

    private handleMessage(ws: WebSocketClient, message: string): void {
        try {
            const parsedMessage = JSON.parse(message.toString()) as WebSocketMessage;
            console.log('Received message:', parsedMessage);

            switch (parsedMessage.type) {
                case 'new_message':
                    if (!ws.userId) {
                        throw new Error('User not authenticated');
                    }
                    // Add the chat to client's subscriptions if not already present
                    if (ws.chatIds && !ws.chatIds.has(parsedMessage.chatId)) {
                        ws.chatIds.add(parsedMessage.chatId);
                    }
                    
                    // Save message to database and let MessageService handle the broadcast
                    this.getMessageService().createMessage({
                        chat_id: parsedMessage.chat_id,
                        sender_id: ws.userId,
                        content: parsedMessage.content,
                        message_type: parsedMessage.message_type || 'text',
                        metadata: parsedMessage.metadata || {},
                        attachments: parsedMessage.attachments || []
                    }).catch(error => {
                        console.error('Error saving message:', error);
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: error.message
                        }));
                    });
                    console.log('Message saved:', 'sent message from websocketService');
                    break;
                
                case 'subscribe_chat':
                    if (!ws.chatIds || !parsedMessage.chat_id || !ws.userId) break;
                    
                    // Immediately add to WebSocket subscription
                    ws.chatIds.add(parsedMessage.chat_id);
                    ws.send(JSON.stringify({
                        type: 'subscribed',
                        chat_id: parsedMessage.chat_id
                    }));

                    // Update database in background
                    this.updateSubscriptionStatus(ws.userId, parsedMessage.chat_id, true)
                        .catch(error => {
                            console.error('Failed to update subscription in database:', error);
                        });
                    break;
                
                case 'unsubscribe_chat':
                    if (!ws.chatIds || !parsedMessage.chat_id || !ws.userId) {
                        console.log('Invalid unsubscribe request:', parsedMessage);
                        break;
                    }

                    // Immediately remove from WebSocket subscription
                    ws.chatIds.delete(parsedMessage.chat_id);
                    ws.send(JSON.stringify({
                        type: 'unsubscribed',
                        chat_id: parsedMessage.chat_id
                    }));

                    // Update database in background
                    this.updateSubscriptionStatus(ws.userId, parsedMessage.chat_id, false)
                        .catch(error => {
                            console.error('Failed to update subscription in database:', error);
                        });
                    break;
                
                case 'mark_messages_as_read':
                    if (!ws.userId || !parsedMessage.chat_id || typeof parsedMessage.last_read_sequence !== 'number') {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Invalid message_read parameters'
                        }));
                            break;
                        }
    
                        // Mark messages as read in the database
                        console.log('Marking messages as read from ws service case:', parsedMessage);
                        this.getMessageService().markAsRead(
                            parsedMessage.chat_id,
                            ws.userId,
                            parsedMessage.last_read_sequence
                        ).then(() => {
                            ws.send(JSON.stringify({
                                type: 'marked_messages_as_read',
                                chat_id: parsedMessage.chat_id,
                                user_id: ws.userId,
                                last_read_sequence: parsedMessage.last_read_sequence,
                            }));
                        }).catch(error => {
                            console.error('Failed to mark messages as read:', error);
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Failed to mark messages as read'
                            }));
                        });
                        break;
                
                default:
                    console.log('Unknown message type:', parsedMessage.type);
            }
        } catch (error) {
            console.error('Error handling message:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid message format'
            }));
        }
    }

    public broadcastToChat(chatId: string, message: WebSocketMessage, excludeUserIds?: string[]): void {
        const messageStr = JSON.stringify(message);
        this.clients.forEach(client => {
            if (client.chatIds?.has(chatId) &&
                client.readyState === WebSocket.OPEN &&
                client.userId && // Ensure userId exists
                (!excludeUserIds?.length || !excludeUserIds.includes(client.userId))) {
                client.send(messageStr);
            }
        });
    }

    /**
     * Check if a user is currently connected to WebSocket
     */
    public isUserOnline(userId: string): boolean {
        for (const client of this.clients) {
            if (client.userId === userId && client.readyState === WebSocket.OPEN) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get all connected user IDs in a chat
     */
    public getConnectedUsersInChat(chatId: string): string[] {
        const connectedUsers: string[] = [];
        this.clients.forEach(client => {
            if (client.chatIds?.has(chatId) &&
                client.readyState === WebSocket.OPEN &&
                client.userId) {
                connectedUsers.push(client.userId);
            }
        });
        return connectedUsers;
    }
}