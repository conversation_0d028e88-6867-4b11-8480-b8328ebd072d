'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create ENUM type first (only if it doesn't exist)
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE message_access_enum AS ENUM ('ADMIN_ONLY', 'MEMBERS_ONLY', 'ALL');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Add message_access column to chats table
    await queryInterface.addColumn('chats', 'message_access', {
      type: 'message_access_enum',
      allowNull: true,
      defaultValue: null
    });

    // Add comment to the column
    await queryInterface.sequelize.query(`
      COMMENT ON COLUMN chats.message_access IS 'Defines which roles have messaging access in the chat - ADMIN_ONLY, MEMBERS_ONLY, or ALL. NULL means no restrictions (default behavior)';
    `);

    // Create index on message_access for better query performance
    await queryInterface.addIndex('chats', ['message_access'], {
      name: 'idx_chats_message_access'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    await queryInterface.removeIndex('chats', 'idx_chats_message_access');

    // Remove column
    await queryInterface.removeColumn('chats', 'message_access');

    // Drop the ENUM type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS message_access_enum;
    `);
  }
};
