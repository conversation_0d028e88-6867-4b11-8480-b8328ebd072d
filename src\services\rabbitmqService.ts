import amqp, { Connection, Channel } from 'amqplib';
import { rabbitmqConfig } from '../config/rabbitmq';

/**
 * Interface for AI message payload that will be sent to RabbitMQ
 */
interface AiMessagePayload {
    user_id: string;
    sai_user_id: string;
    chat_id: string;
    message: string;
    sai_reference: string;
    user_reference: string;
    sequence_number: number;
    created_at: Date;
}

/**
 * Service to handle RabbitMQ operations
 * Implements singleton pattern to maintain a single connection
 */
export class RabbitMQService {
    private static instance: RabbitMQService;
    private connection: any = null; // Changed from Connection to any to avoid type issues
    private channel: Channel | null = null;

    private constructor() {}

    /**
     * Get singleton instance of RabbitMQService
     */
    public static getInstance(): RabbitMQService {
        if (!RabbitMQService.instance) {
            RabbitMQService.instance = new RabbitMQService();
        }
        return RabbitMQService.instance;
    }

    /**
     * Initialize RabbitMQ connection and channel
     * Sets up exchanges and queues
     */
    public async initialize(): Promise<void> {
        try {
            // Create connection
            this.connection = await amqp.connect(rabbitmqConfig.connection);
            console.log('Successfully connected to RabbitMQ');

            // Create channel
            this.channel = await this.connection.createChannel();
            console.log('Successfully created RabbitMQ channel');

            if (!this.channel) {
                throw new Error('RabbitMQ channel not available');
            }

            // Setup exchange
            await this.channel.assertExchange(
                rabbitmqConfig.exchanges.aiMessages,
                'direct',
                { durable: true }
            );

            // Setup queue
            await this.channel.assertQueue(rabbitmqConfig.queues.aiMessages, {
                durable: true
            });

            // Bind queue to exchange
            await this.channel.bindQueue(
                rabbitmqConfig.queues.aiMessages,
                rabbitmqConfig.exchanges.aiMessages,
                rabbitmqConfig.routingKeys.aiMessages
            );

            // Handle connection closure
            this.connection.on('close', () => {
                console.log('RabbitMQ connection closed');
                this.connection = null;
                this.channel = null;
            });
        } catch (error) {
            console.error('Error initializing RabbitMQ:', error);
            throw error;
        }
    }

    /**
     * Publish AI message to RabbitMQ
     * @param payload Message payload to be sent
     */
    public async publishAiMessage(payload: AiMessagePayload): Promise<void> {
        try {
            if (!this.channel) {
                await this.initialize();
            }

            if (!this.channel) {
                throw new Error('RabbitMQ channel not available');
            }

            await this.channel.publish(
                rabbitmqConfig.exchanges.aiMessages,
                rabbitmqConfig.routingKeys.aiMessages,
                Buffer.from(JSON.stringify(payload)),
                { persistent: true }
            );

            console.log('Successfully published message to RabbitMQ:', {
                exchange: rabbitmqConfig.exchanges.aiMessages,
                routingKey: rabbitmqConfig.routingKeys.aiMessages,
                chat_id: payload.chat_id,
                sequence_number: payload.sequence_number
            });
        } catch (error) {
            console.error('Error publishing message to RabbitMQ:', error);
            throw error;
        }
    }

    /**
     * Close RabbitMQ connection
     */
    public async close(): Promise<void> {
        try {
            if (this.channel) {
                await this.channel.close();
            }
            if (this.connection) {
                await this.connection.close();
            }
        } catch (error) {
            console.error('Error closing RabbitMQ connection:', error);
            throw error;
        }
    }
}
