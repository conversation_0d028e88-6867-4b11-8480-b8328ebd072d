    export const up = async (queryInterface, Sequelize) => {
  // chat_members table
  await queryInterface.createTable('chat_members', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER
    },
    chat_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'chats',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    user_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    last_read_sequence: {
      type: Sequelize.INTEGER,
      defaultValue: 0
    },
    joined_at: {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW
    }
  });

  // Add unique constraint to prevent duplicate chat members
  await queryInterface.addConstraint('chat_members', {
    fields: ['chat_id', 'user_id'],
    type: 'unique',
    name: 'unique_chat_member'
  });
};

export const down = async (queryInterface) => {
  // drop table
  await queryInterface.dropTable('chat_members');
};
