import dotenv from 'dotenv';
dotenv.config();

interface Neo4jConfig {
    host: string;
    port: string;
    username: string;
    password: string;
    database: string;
}

const neo4jConfig: Neo4jConfig = {
    host: process.env.NEO4J_HOST || '**************',
    port: process.env.NEO4J_PORT || '8001',
    username: process.env.NEO4J_USERNAME || 'neo4j',
    password: process.env.NEO4J_PASSWORD || 'password',
    database: process.env.NEO4J_DATABASE || 'neo4j'
};

export default {
    neo4j: neo4jConfig
};
