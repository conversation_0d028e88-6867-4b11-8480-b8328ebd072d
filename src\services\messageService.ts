import { Pool } from 'pg';
import { WebSocketService } from './websocketService';
import { RabbitMQService } from './rabbitmqService';
import { FirebaseService } from './firebaseService';
import { DatabaseError } from '../utils/errors';
import { config } from '../config/env';

interface MessageQuery {
    chat_id: string;
    before_sequence?: number;
    after_sequence?: number;
    limit?: number;
}

interface MessageFromSequenceQuery {
    chat_id: string;
    anchor_sequence: number;
    direction: 'PAST' | 'FUTURE' | 'BI-DIRECTION';
    limit: number;
}

interface CreateMessageParams {
    chat_id: string;
    sender_id: string;
    content: string;
    message_type: string;
    metadata?: any;
    attachments?: any[];
    object?: string;
}

export class MessageService {
    private pool: Pool;
    private wsService?: WebSocketService;
    private firebaseService?: FirebaseService;

    constructor() {
        this.pool = new Pool(config.database);
    }

    private getWsService(): WebSocketService {
        if (!this.wsService) {
            this.wsService = WebSocketService.getInstance();
        }
        return this.wsService;
    }

    private getFirebaseService(): FirebaseService {
        if (!this.firebaseService) {
            this.firebaseService = FirebaseService.getInstance();
        }
        return this.firebaseService;
    }

    /**
     * Send FCM push notifications to offline users in a chat
     */
    private async sendPushNotificationsToOfflineUsers(
        chatId: string,
        senderName: string,
        messageContent: string,
        excludeUserId: string
    ): Promise<void> {
        try {
            const client = await this.pool.connect();

            try {
                // Get all chat members except the sender
                const { rows: chatMembers } = await client.query(`
                    SELECT cm.user_id, u.user_reference, u.name, u.username, cm.is_muted
                    FROM chat_members cm
                    JOIN users u ON cm.user_id = u.id
                    WHERE cm.chat_id = $1 AND cm.user_id != $2
                `, [chatId, excludeUserId]);

                const wsService = this.getWsService();
                const firebaseService = this.getFirebaseService();

                for (const member of chatMembers) {
                    // Skip if user has muted this chat
                    if (member.is_muted) {
                        console.log(`User ${member.user_reference} has muted chat ${chatId}, skipping notification`);
                        continue;
                    }

                    // Check if user is online (connected to WebSocket)
                    const isOnline = wsService.isUserOnline(member.user_id);

                    if (!isOnline) {
                        // User is offline, send FCM notification
                        console.log(`User ${member.user_reference} is offline, sending FCM notification`);

                        const notificationTitle = senderName;
                        const notificationBody = messageContent.length > 100
                            ? messageContent.substring(0, 100) + '...'
                            : messageContent;

                        await firebaseService.sendNotificationToUser(
                            member.user_reference,
                            {
                                title: notificationTitle,
                                body: notificationBody,
                            },
                            {
                                chat_id: chatId,
                                sender_id: excludeUserId,
                                message_type: 'new_message'
                            }
                        );
                    } else {
                        console.log(`User ${member.user_reference} is online, skipping FCM notification`);
                    }
                }
            } finally {
                client.release();
            }
        } catch (error) {
            console.error('Error sending push notifications:', error);
            // Don't throw error as this is not critical for message delivery
        }
    }

    async getMessages(query: MessageQuery) {
        const { chat_id, before_sequence, after_sequence, limit = 20 } = query;
        console.log('SQL Query parameters:', { chat_id, before_sequence, after_sequence, limit });
        let sql = `
            SELECT 
                m.*,
                u.username as sender_name,
                m.id as message_id,
                m.chat_id as chat_id,
                m.sender_id
                m.object
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE m.chat_id = $1
            AND m.sequence_number > $2
        `;

        const params: any[] = [chat_id, after_sequence];

        if (before_sequence) {
            sql += ` AND m.sequence_number < $3`;
            params.push(before_sequence);
        }

        sql += ` ORDER BY m.sequence_number ASC LIMIT $${params.length + 1}`;
        params.push(limit);

        try {
            console.log('Executing SQL query:', { sql, params });
            const { rows } = await this.pool.query(sql, params);
            console.log('SQL Query result:', rows);
            return rows.map(row => ({
                message_id: row.id,
                chat_id: row.chat_id,
                sender_id: row.sender_id,
                content: row.content,
                message_type: row.message_type,
                metadata: row.metadata,
                attachments: row.attachments,
                object: row.object,
                sequence_number: parseInt(row.sequence_number),
                created_at: row.created_at,
                updated_at: row.updated_at,
                sender_name: row.sender_name
            }));
        } catch (error) {
            throw new DatabaseError('Error fetching messages');
        }
    }

    async getMessagesFromSequence(query: MessageFromSequenceQuery) {
        const { chat_id, anchor_sequence, direction, limit } = query;
        let sql = `
            SELECT 
                m.*,
                u.username as sender_name,
                m.id as message_id,
                m.chat_id as chat_id,
                m.sender_id,
                m.object
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE m.chat_id = $1
        `;
        const params: any[] = [chat_id];
        let orderBy = '';

        if (direction === 'PAST') {
            sql += ' AND m.sequence_number < $2';
            params.push(anchor_sequence);
            orderBy = 'DESC';
        } else if (direction === 'FUTURE') {
            sql += ' AND m.sequence_number > $2';
            params.push(anchor_sequence);
            orderBy = 'ASC';
        } else {
            // BI-DIRECTION
            const halfLimit = Math.floor(limit / 2);
            sql = `
                (${sql} AND m.sequence_number < $2 ORDER BY m.sequence_number DESC LIMIT $3)
                UNION ALL
                (${sql} AND m.sequence_number >= $2 ORDER BY m.sequence_number ASC LIMIT $4)
            `;
            params.push(anchor_sequence, halfLimit, limit - halfLimit);
            orderBy = 'ASC';
        }

        if (direction !== 'BI-DIRECTION') {
            sql += ` ORDER BY m.sequence_number ${orderBy} LIMIT $${params.length + 1}`;
            params.push(limit);
        }

        try {
            console.log('Executing SQL query:', { sql, params });
            const { rows } = await this.pool.query(sql, params);
            console.log('SQL Query result:', rows);
            
            const messages = rows.map(row => ({
                message_id: row.id,
                chat_id: row.chat_id,
                sender_id: row.sender_id,
                content: row.content,
                message_type: row.message_type,
                metadata: row.metadata,
                attachments: row.attachments,
                object: row.object,
                sequence_number: parseInt(row.sequence_number),
                created_at: row.created_at,
                updated_at: row.updated_at,
                sender_name: row.sender_name
            }));

            // For BI-DIRECTION, ensure final order is by sequence_number ASC
            return direction === 'BI-DIRECTION' 
                ? messages.sort((a, b) => a.sequence_number - b.sequence_number)
                : messages;
        } catch (error: any) {
            throw new DatabaseError(`Error fetching messages: ${error.message}`);
        }
    }

    async createMessage(params: CreateMessageParams) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');
            console.log('Inserting message with params:', JSON.stringify(params, null, 2));

            // Check if sender is a member of the chat
            const { rows: chatMembers } = await client.query(`
                SELECT * FROM chat_members WHERE chat_id = $1 AND user_id = $2
            `, [params.chat_id, params.sender_id]);

            if (chatMembers.length === 0) {
                throw new Error('Sender is not a member of the chat');
            }

            // Fetch and increment last_sequence_number for the chat
            const { rows: [sequenceResult] } = await client.query(`
                UPDATE chats
                SET last_sequence_number = last_sequence_number + 1
                WHERE id = $1
                RETURNING last_sequence_number
            `, [params.chat_id]);
            console.log('Sequence result:', sequenceResult);


            const { rows: [message] } = await client.query(`
                INSERT INTO messages 
                (chat_id, sender_id, content, message_type, metadata, attachments, object, sequence_number)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            `, [
                params.chat_id,
                params.sender_id,
                params.content,
                params.message_type,
                params.metadata || {},
                JSON.stringify(params.attachments || []),
                params.object || '',
                sequenceResult.last_sequence_number
            ]);

            // Update unread_count for all chat members except the sender
            await client.query(`
                UPDATE chat_members 
                SET unread_count = unread_count + 1
                WHERE chat_id = $1 AND user_id != $2
            `, [params.chat_id, params.sender_id]);

            // Check if chat has a store AI user and queue message if found
            const { rows: storeAiMembers } = await client.query(`
                SELECT cm.user_id, u.user_reference
                FROM chat_members cm
                JOIN users u ON u.id = cm.user_id
                WHERE cm.chat_id = $1 
                AND u.user_reference LIKE 'SAI_%'
                AND length(u.user_reference) = 17
            `, [params.chat_id]);

            console.log('storeAiMembers', storeAiMembers);

            if (storeAiMembers.length > 0) {
                const storeAiUser = storeAiMembers[0];
                // Get sender's user_reference
                const { rows: [sender] } = await client.query(`
                    SELECT user_reference FROM users WHERE id = $1
                `, [params.sender_id]);

                // Only queue messages if sender is not an AI user
                if (!sender.user_reference.startsWith('SAI_')) {
                    // Send message to RabbitMQ instead of PostgreSQL
                    const rabbitMQService = RabbitMQService.getInstance();
                    await rabbitMQService.publishAiMessage({
                        user_id: params.sender_id,
                        sai_user_id: storeAiUser.user_id,
                        chat_id: params.chat_id,
                        message: params.content,
                        sai_reference: storeAiUser.user_reference,
                        user_reference: sender.user_reference,
                        sequence_number: parseInt(sequenceResult.last_sequence_number),
                        created_at: message.created_at
                    });
                    console.log('Successfully published message to RabbitMQ queue');
                }
            }

            console.log('after query', message);

            await client.query('COMMIT');
            console.log('after committing');
            console.log('before broadcasting');

            const formattedMessage = {
                message_id: message.id,
                chat_id: message.chat_id,
                sender_id: message.sender_id,
                content: message.content,
                message_type: message.message_type,
                metadata: message.metadata,
                attachments: message.attachments,
                object: message.object,
                sequence_number: parseInt(message.sequence_number),
                created_at: message.created_at,
                updated_at: message.updated_at
            };

            // Notify connected clients
            this.getWsService().broadcastToChat(params.chat_id, {
                type: 'new_message',
                chat_id: params.chat_id,
                message: formattedMessage
            });

            // Get sender information for push notifications
            const { rows: [senderInfo] } = await client.query(`
                SELECT name, username FROM users WHERE id = $1
            `, [params.sender_id]);

            const senderName = senderInfo?.name || senderInfo?.username || 'Someone';

            // Send FCM push notifications to offline users
            this.sendPushNotificationsToOfflineUsers(
                params.chat_id,
                senderName,
                params.content,
                params.sender_id
            ).catch(error => {
                console.error('Error sending push notifications:', error);
            });

            // Get updated chat details for all members except sender
            const { rows: chatDetails } = await client.query(`
                SELECT DISTINCT
                    c.id AS chat_id,
                    c.chat_type,
                    u.username AS chat_name,
                    u.user_icon AS chat_icon,
                    u.entity_type,
                    cm.user_id,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    c.created_at,
                    c.updated_at
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id
                JOIN users u ON u.id = (
                    SELECT user_id 
                    FROM chat_members 
                    WHERE chat_id = c.id 
                    AND user_id != cm.user_id 
                    LIMIT 1
                )
                WHERE c.id = $1 AND cm.user_id != $2
            `, [params.chat_id, params.sender_id]);

            // Broadcast chat updates to all members except sender
            for (const chatDetail of chatDetails) {
                this.getWsService().broadcastToChat(params.chat_id, {
                    type: 'chat_update',
                    chats: [{
                        chat_id: chatDetail.chat_id,
                        chat_icon: chatDetail.chat_icon,
                        chat_name: chatDetail.chat_name,
                        chat_type: chatDetail.chat_type,
                        user_id: chatDetail.user_id,
                        created_at: chatDetail.created_at,
                        updated_at: chatDetail.updated_at,
                        last_read_sequence: chatDetail.last_read_sequence,
                        unread_count: chatDetail.unread_count,
                        is_subscribed: chatDetail.is_subscribed,
                        entity_type: chatDetail.entity_type
                    }]
                }, [params.sender_id]);
            }

            return formattedMessage;
        } catch (error) {
            console.error('Full error details:', error);
            await client.query('ROLLBACK');
            throw new DatabaseError(`Error creating message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
            client.release();
        }
    }

    /**
     * Marks messages in a chat as read for a specific user.
     * 
     * This method updates the last read sequence for a chat member and calculates 
     * the number of remaining unread messages. It ensures that:
     * - Only messages sent by other users after the last read sequence are counted
     * - The unread message count is accurately updated
     * - The chat member's read status is updated within a database transaction
     * 
     * @param chatId - The unique identifier of the chat
     * @param userId - The unique identifier of the user marking messages as read
     * @param lastReadSequence - The sequence number up to which messages are considered read
     * 
     * @throws {DatabaseError} If there's an issue with database operations
     * 
     * @remarks
     * - Performs a database transaction to ensure data consistency
     * - Broadcasts a 'message_read' event to other chat members
     * - Calculates unread messages by counting messages:
     *   1. In the same chat
     *   2. With sequence number greater than lastReadSequence
     *   3. Not sent by the current user
     * 
     * @example
     * ```typescript
     * await messageService.markAsRead(
     *   'chat-uuid-123', 
     *   'user-uuid-456', 
     *   42
     * );
     * ```
     */
    async markAsRead(chatId: string, userId: string, lastReadSequence: number) {
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            console.log('Marking messages as read:', { chat_id: chatId, user_id: userId, last_read_sequence: lastReadSequence });
            // Calculate remaining unread messages (those after lastReadSequence)
            const { rows: [{ remaining_unread }] } = await client.query(`
                SELECT COUNT(*) as remaining_unread
                FROM messages m
                WHERE m.chat_id = $1 
                AND m.sequence_number > $2
                AND m.sender_id != $3
            `, [chatId, lastReadSequence, userId]);

            console.log('Remaining unread messages:', remaining_unread);

            // Update last_read_sequence and set exact unread_count
            await client.query(`
                UPDATE chat_members
                SET last_read_sequence = $1,
                    unread_count = $2
                WHERE chat_id = $3 AND user_id = $4 
                AND last_read_sequence < $1
            `, [lastReadSequence, remaining_unread, chatId, userId]);
            
            console.log('Updated chat member read status');
            await client.query('COMMIT');

            // send chat update to sender with new chat details
            const { rows: chatDetails } = await client.query(`
                SELECT DISTINCT
                    c.id AS chat_id,
                    c.chat_type,
                    u.username AS chat_name,
                    u.user_icon AS chat_icon,
                    u.entity_type,
                    cm.user_id,
                    cm_other.user_id as other_user_id,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    c.created_at,
                    c.updated_at
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id
                JOIN chat_members cm_other ON c.id = cm_other.chat_id AND cm_other.user_id != cm.user_id
                JOIN users u ON u.id = (
                    SELECT user_id 
                    FROM chat_members 
                    WHERE chat_id = c.id 
                    AND user_id != cm.user_id 
                    LIMIT 1
                )
                WHERE c.id = $1 AND cm.user_id = $2
            `, [chatId, userId]);

            // Broadcast chat updates to all members except the other member
            for (const chatDetail of chatDetails) {
                this.getWsService().broadcastToChat(chatId, {
                    type: 'chat_update',
                    chats: [{
                        chat_id: chatDetail.chat_id,
                        chat_icon: chatDetail.chat_icon,
                        chat_name: chatDetail.chat_name,
                        chat_type: chatDetail.chat_type,
                        user_id: chatDetail.user_id,
                        created_at: chatDetail.created_at,
                        updated_at: chatDetail.updated_at,
                        last_read_sequence: chatDetail.last_read_sequence,
                        unread_count: chatDetail.unread_count,
                        is_subscribed: chatDetail.is_subscribed,
                        entity_type: chatDetail.entity_type
                    }]
                }, chatDetail.other_user_id);
            }

        } catch (error) {
            await client.query('ROLLBACK');
            throw new DatabaseError('Error marking messages as read');
        } finally {
            client.release();
        }
    }

    async deleteMessage(messageId: string, userId: string) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            const { rows: [message] } = await client.query(`
                DELETE FROM messages
                WHERE id = $1 AND sender_id = $2
                RETURNING chat_id
            `, [messageId, userId]);

            if (!message) {
                throw new Error('Message not found or unauthorized');
            }

            await client.query('COMMIT');

            // Notify connected clients
            this.getWsService().broadcastToChat(message.chat_id, {
                type: 'message_deleted',
                chatId: message.chat_id,
                messageId
            }, [userId]);

        } catch (error) {
            await client.query('ROLLBACK');
            console.log(error);
            throw new DatabaseError('Error deleting message');
        } finally {
            client.release();
        }
    }

    async getMessage(messageId: string) {
        try {
            const { rows: [message] } = await this.pool.query(`
                SELECT 
                    m.*,
                    m.id as message_id,
                    m.chat_id as chat_id,
                    m.sender_id as sender_id,
                    u.username as sender_name,
                    m.object
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.id = $1
            `, [messageId]);
            
            if (!message) return null;
            
            return {
                message_id: message.message_id,
                chat_id: message.chat_id,
                sender_id: message.sender_id,
                content: message.content,
                message_type: message.message_type,
                metadata: message.metadata,
                attachments: message.attachments,
                object: message.object,
                sequence_number: parseInt(message.sequence_number),
                created_at: message.created_at,
                updated_at: message.updated_at,
                sender_name: message.sender_name
            };
        } catch (error) {
            throw new DatabaseError('Error fetching message');
        }
    }
}