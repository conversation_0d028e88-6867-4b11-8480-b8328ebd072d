#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create group chats for all existing stores with a static user.

This script:
1. Connects to both the main Swadesic database and the messaging database
2. Fetches all stores from the main database that have been migrated to messaging
3. Gets the STATIC_USER_ID from .env file
4. Creates group chats for each store with the static user as a member
5. Assigns roles: Store as ADMIN, Static User as MEMBER
6. Sets meaningful chat names: "Group Chat - {store_name}"
7. <PERSON>les duplicate prevention and error logging

Usage:
    python create_store_group_chats.py
"""

import psycopg2
import psycopg2.extras
import logging
import sys
from typing import Dict, List, Optional
from decouple import config
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('group_chat_creation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GroupChatCreator:
    def __init__(self):
        """Initialize the GroupChatCreator with database configurations."""
        # Main Swadesic database configuration
        self.swadesic_db_config = {
            'dbname': config('SWADESIC_DB_NAME'),
            'user': config('SWADESIC_DB_USER'),
            'password': config('SWADESIC_DB_PASSWORD'),
            'host': config('SWADESIC_DB_HOST'),
            'port': config('SWADESIC_DB_PORT')
        }
        
        # Messaging database configuration
        self.messaging_db_config = {
            'dbname': config('DB_NAME'),
            'user': config('DB_USER'),
            'password': config('DB_PASSWORD'),
            'host': config('DB_HOST'),
            'port': config('DB_PORT')
        }
        
        # Static user ID from .env
        self.static_user_id = config('STATIC_USER_ID')
        logger.info(f"Using static user ID: {self.static_user_id}")

    def connect_to_database(self, db_config: Dict) -> psycopg2.extensions.connection:
        """Connect to a PostgreSQL database."""
        try:
            conn = psycopg2.connect(**db_config)
            conn.autocommit = False
            logger.info(f"Connected to database: {db_config['dbname']}")
            return conn
        except psycopg2.Error as e:
            logger.error(f"Failed to connect to database {db_config['dbname']}: {e}")
            raise

    def fetch_migrated_stores(self, swadesic_conn: psycopg2.extensions.connection) -> List[Dict]:
        """Fetch all stores that have been migrated to messaging system."""
        try:
            cursor = swadesic_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            query = """
                SELECT 
                    store_reference,
                    storehandle,
                    store_name,
                    icon,
                    new_messaging_user_id,
                    new_messaging_token
                FROM "store"."store" 
                WHERE deleted = false 
                AND new_messaging_user_id IS NOT NULL
                AND new_messaging_token IS NOT NULL
                ORDER BY store_name
            """
            cursor.execute(query)
            stores = cursor.fetchall()
            cursor.close()
            
            logger.info(f"Found {len(stores)} migrated stores")
            return [dict(store) for store in stores]
            
        except psycopg2.Error as e:
            logger.error(f"Failed to fetch migrated stores: {e}")
            raise

    def check_existing_chat(self, messaging_conn: psycopg2.extensions.connection,
                           store_user_id: str) -> Optional[str]:
        """Check if a group chat already exists between static user and store.
        Uses the same logic as ChatService.createChat method."""
        try:
            cursor = messaging_conn.cursor()
            # Use the same query logic as ChatService.createChat
            query = """
                SELECT DISTINCT
                    c.id as chat_id,
                    c.chat_type,
                    c.created_at,
                    c.updated_at,
                    CAST(c.last_sequence_number AS INTEGER) as last_sequence_number
                FROM chats c
                JOIN chat_members cm1 ON c.id = cm1.chat_id
                JOIN chat_members cm2 ON c.id = cm2.chat_id
                WHERE cm1.user_id = %s
                AND cm2.user_id = %s
                AND c.chat_type = %s
                LIMIT 1
            """
            cursor.execute(query, (self.static_user_id, store_user_id, 'GROUP'))
            result = cursor.fetchone()
            cursor.close()

            return result[0] if result else None

        except psycopg2.Error as e:
            logger.error(f"Failed to check existing chat for store {store_user_id}: {e}")
            return None

    def create_group_chat(self, messaging_conn: psycopg2.extensions.connection,
                         store_user_id: str, store_name: str) -> Optional[str]:
        """Create a group chat between static user and store.
        Uses the same logic as ChatService.createChat method."""
        try:
            cursor = messaging_conn.cursor()

            # Begin transaction
            cursor.execute("BEGIN")

            # Create new chat with chat_name for group identification
            chat_name = f"{store_name}'s updates"
            cursor.execute("""
                INSERT INTO chats (chat_type, chat_name, message_access)
                VALUES (%s, %s, %s)
                RETURNING
                    id as chat_id,
                    chat_type,
                    chat_name,
                    created_at,
                    updated_at,
                    CAST(last_sequence_number AS INTEGER) as last_sequence_number
            """, ('GROUP', chat_name, 'ADMIN_ONLY'))
            new_chat = cursor.fetchone()
            chat_id = new_chat[0]

            # Add members with appropriate roles
            # Static user as MEMBER, Store as ADMIN
            cursor.execute("""
                INSERT INTO chat_members (chat_id, user_id, joined_at, role_type, is_subscribed)
                VALUES (%s, %s, CURRENT_TIMESTAMP,%s, true)
            """, (chat_id, self.static_user_id, 'MEMBER'))

            cursor.execute("""
                INSERT INTO chat_members (chat_id, user_id, joined_at, role_type, is_subscribed)
                VALUES (%s, %s, CURRENT_TIMESTAMP, %s, true)
            """, (chat_id, store_user_id, 'ADMIN'))

            # Commit transaction
            cursor.execute("COMMIT")
            cursor.close()

            logger.info(f"Created group chat {chat_id} for store: {store_name} (Store: ADMIN, Static User: MEMBER)")
            return chat_id

        except psycopg2.Error as e:
            cursor.execute("ROLLBACK")
            cursor.close()
            logger.error(f"Failed to create group chat for store {store_name}: {e}")
            return None

    def verify_static_user_exists(self, messaging_conn: psycopg2.extensions.connection) -> bool:
        """Verify that the static user exists in the messaging database."""
        try:
            cursor = messaging_conn.cursor()
            cursor.execute("SELECT id, username FROM users WHERE id = %s", (self.static_user_id,))
            result = cursor.fetchone()
            cursor.close()
            
            if result:
                logger.info(f"Static user found: {result[1]} ({result[0]})")
                return True
            else:
                logger.error(f"Static user {self.static_user_id} not found in messaging database")
                return False
                
        except psycopg2.Error as e:
            logger.error(f"Failed to verify static user: {e}")
            return False

    def run(self) -> Dict:
        """Main execution method."""
        results = {
            'total_stores': 0,
            'chats_created': 0,
            'chats_existing': 0,
            'errors': 0,
            'created_chats': [],
            'existing_chats': [],
            'error_details': []
        }
        
        swadesic_conn = None
        messaging_conn = None
        
        try:
            # Connect to databases
            swadesic_conn = self.connect_to_database(self.swadesic_db_config)
            messaging_conn = self.connect_to_database(self.messaging_db_config)
            
            # Verify static user exists
            if not self.verify_static_user_exists(messaging_conn):
                logger.error("Static user verification failed. Aborting.")
                return results
            
            # Fetch migrated stores
            stores = self.fetch_migrated_stores(swadesic_conn)
            results['total_stores'] = len(stores)
            
            if not stores:
                logger.warning("No migrated stores found")
                return results
            
            # Process each store
            for store in stores:
                store_user_id = store['new_messaging_user_id']
                store_name = store['store_name'] or store['storehandle'] or store['store_reference']
                
                try:
                    # Check if chat already exists
                    existing_chat_id = self.check_existing_chat(messaging_conn, store_user_id)
                    
                    if existing_chat_id:
                        results['chats_existing'] += 1
                        results['existing_chats'].append({
                            'store_reference': store['store_reference'],
                            'store_name': store_name,
                            'store_user_id': store_user_id,
                            'chat_id': existing_chat_id
                        })
                        logger.info(f"Chat already exists for store {store_name}: {existing_chat_id}")
                        continue
                    
                    # Create new group chat
                    chat_id = self.create_group_chat(messaging_conn, store_user_id, store_name)
                    
                    if chat_id:
                        results['chats_created'] += 1
                        results['created_chats'].append({
                            'store_reference': store['store_reference'],
                            'store_name': store_name,
                            'store_user_id': store_user_id,
                            'chat_id': chat_id
                        })
                    else:
                        results['errors'] += 1
                        results['error_details'].append({
                            'store_reference': store['store_reference'],
                            'store_name': store_name,
                            'store_user_id': store_user_id,
                            'error': 'Failed to create chat'
                        })
                        
                except Exception as e:
                    results['errors'] += 1
                    results['error_details'].append({
                        'store_reference': store['store_reference'],
                        'store_name': store_name,
                        'store_user_id': store_user_id,
                        'error': str(e)
                    })
                    logger.error(f"Error processing store {store_name}: {e}")
            
        except Exception as e:
            logger.error(f"Critical error during execution: {e}")
            results['errors'] += 1
            
        finally:
            # Close database connections
            if swadesic_conn:
                swadesic_conn.close()
                logger.info("Closed Swadesic database connection")
            if messaging_conn:
                messaging_conn.close()
                logger.info("Closed messaging database connection")
        
        return results

def main():
    """Main function to run the group chat creation script."""
    logger.info("Starting group chat creation script")
    logger.info("=" * 50)
    
    creator = GroupChatCreator()
    results = creator.run()
    
    # Print summary
    logger.info("=" * 50)
    logger.info("EXECUTION SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Total stores processed: {results['total_stores']}")
    logger.info(f"Group chats created: {results['chats_created']}")
    logger.info(f"Existing chats found: {results['chats_existing']}")
    logger.info(f"Errors encountered: {results['errors']}")
    
    if results['created_chats']:
        logger.info("\nNewly created chats:")
        for chat in results['created_chats']:
            logger.info(f"  - {chat['store_name']}: {chat['chat_id']}")
    
    if results['existing_chats']:
        logger.info("\nExisting chats:")
        for chat in results['existing_chats']:
            logger.info(f"  - {chat['store_name']}: {chat['chat_id']}")
    
    if results['error_details']:
        logger.info("\nErrors:")
        for error in results['error_details']:
            logger.info(f"  - {error['store_name']}: {error['error']}")
    
    logger.info("=" * 50)
    logger.info("Script execution completed")

if __name__ == "__main__":
    main()
