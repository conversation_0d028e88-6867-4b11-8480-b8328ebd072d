import { Pool } from 'pg';
import { DatabaseError } from '../utils/errors';
import { config } from '../config/env';
import { Neo4jService } from './neo4jService';

interface CreateChatParams {
    chatType: string;
    creatorId: string;
    memberIds: string[];
    chatName?: string;
    messageAccess?: string;
}

interface RecommendedContact {
    chat_id: string | null;
    chat_icon: string | null;
    chat_name: string;
    chat_type: string;
    connecting_id: string;
    entity_type: string;
    last_read_sequence: number;
    unread_count: number;
    is_subscribed: boolean;
    created_at: Date;
    updated_at: Date;
}

export class ChatService {
    private pool: Pool;

    constructor() {
        this.pool = new Pool(config.database);
    }

    //method to get all chats of a user
    async getUserChats(userId: string) {
        try {
            const { rows } = await this.pool.query(`
                WITH user_chats AS (
                    SELECT chat_id
                    FROM chat_members
                    WHERE user_id = $1
                ),
                chat_preview AS (
                    SELECT DISTINCT ON (m.chat_id)
                        m.chat_id,
                        m.content,
                        m.created_at as message_timestamp,
                        m.sender_id,
                        u.username as sender_username
                    FROM messages m
                    JOIN users u ON m.sender_id = u.id
                    WHERE m.chat_id IN (SELECT chat_id FROM user_chats)
                    ORDER BY m.chat_id, m.sequence_number DESC
                ),
                chat_admin AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as admin_user_id
                    FROM chat_members
                    WHERE role_type = 'ADMIN'
                    ORDER BY chat_id, joined_at ASC
                ),
                chat_other_member AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as other_user_id
                    FROM chat_members
                    WHERE user_id <> $1
                    ORDER BY chat_id, joined_at ASC
                )
                SELECT DISTINCT
                    c.id AS chat_id,
                    c.chat_type,
                    COALESCE(c.chat_name, u.username) AS chat_name,
                    c.message_access,
                    u.user_icon AS chat_icon,
                    u.user_reference as entity_reference,
                    u.id AS user_id,
                    u.entity_type,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    cm.role_type,
                    cm.is_muted::BOOLEAN,
                    c.created_at,
                    c.updated_at,
                    jsonb_build_object(
                        'preview_text',
                        CASE
                            WHEN lm.sender_id = $1 THEN CONCAT('You: ', LEFT(lm.content, 20))
                            ELSE CONCAT(LEFT(lm.content, 20))
                        END,
                        'timestamp', lm.message_timestamp
                    ) as chat_preview
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id AND cm.user_id = $1
                LEFT JOIN chat_admin ca ON c.id = ca.chat_id
                LEFT JOIN chat_other_member com ON c.id = com.chat_id
                JOIN users u ON
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u.id = ca.admin_user_id
                        ELSE u.id = com.other_user_id
                    END
                LEFT JOIN chat_preview lm ON c.id = lm.chat_id
                ORDER BY c.updated_at DESC;
            `, [userId]);

            return rows.map(chat => ({
                chat_id: chat.chat_id,
                chat_icon: chat.chat_icon,
                chat_name: chat.chat_name || 'Group Chat',
                chat_type: chat.chat_type,
                message_access: chat.message_access,
                user_id: chat.user_id,
                entity_reference: chat.entity_reference,
                created_at: chat.created_at,
                updated_at: chat.updated_at,
                last_read_sequence: chat.last_read_sequence,
                unread_count: chat.unread_count,
                is_subscribed: chat.is_subscribed,
                role_type: chat.role_type,
                is_muted: chat.is_muted,
                entity_type: chat.entity_type,
                chat_preview: chat.chat_preview
            }));
        } catch (error) {
            console.error(error);
            throw new DatabaseError('Error fetching user chats');
        }
    }

    /**
     * Get user chats based on subscription status and recommended contacts
     * @param userId - Current user's ID
     * @param userReference - Current user's reference
     * @param chatType - Type of chats to fetch ('ACTIVE' or 'REQUEST')
     * @param limit - Number of items to return
     * @param offset - Number of items to skip
     * @returns Array of chats and recommended contacts
     */
    async getUserChatsNew(userId: string, userReference: string, chatType: 'ACTIVE' | 'REQUEST', limit: number, offset: number) {
        try {
            console.log('User ID:', userId);
            console.log('User Reference:', userReference);
            // Get chats based on subscription status
            const { rows: chats } = await this.pool.query(`
                WITH user_chats AS (
                    SELECT chat_id
                    FROM chat_members
                    WHERE user_id = $1 AND is_subscribed = $2
                ),
                chat_preview AS (
                    SELECT DISTINCT ON (m.chat_id)
                        m.chat_id,
                        m.content,
                        m.created_at as message_timestamp,
                        m.sender_id,
                        u.username as sender_username
                    FROM messages m
                    JOIN users u ON m.sender_id = u.id
                    WHERE m.chat_id IN (SELECT chat_id FROM user_chats)
                    ORDER BY m.chat_id, m.sequence_number DESC
                ),
                chat_admin AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as admin_user_id
                    FROM chat_members
                    WHERE role_type = 'ADMIN'
                    ORDER BY chat_id, joined_at ASC
                ),
                chat_other_member AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as other_user_id
                    FROM chat_members
                    WHERE user_id <> $1
                    ORDER BY chat_id, joined_at ASC
                )
                SELECT DISTINCT
                    c.id AS chat_id,
                    c.chat_type,
                    COALESCE(c.chat_name, u.username) AS chat_name,
                    c.message_access,
                    u.user_icon AS chat_icon,
                    u.user_reference as entity_reference,
                    u.id AS connecting_id,
                    u.entity_type,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    cm.role_type,
                    cm.is_muted::BOOLEAN,
                    c.created_at,
                    c.updated_at,
                    jsonb_build_object(
                        'preview_text',
                        CASE
                            WHEN cp.sender_id = $1 THEN CONCAT('You: ', LEFT(cp.content, 20))
                            ELSE CONCAT(LEFT(cp.content, 20))
                        END,
                        'timestamp', cp.message_timestamp
                    ) as chat_preview
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id AND cm.user_id = $1
                LEFT JOIN chat_admin ca ON c.id = ca.chat_id
                LEFT JOIN chat_other_member com ON c.id = com.chat_id
                JOIN users u ON
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u.id = ca.admin_user_id
                        ELSE u.id = com.other_user_id
                    END
                LEFT JOIN chat_preview cp ON c.id = cp.chat_id
                WHERE cm.is_subscribed = $2
                ORDER BY c.updated_at DESC
                LIMIT $3 OFFSET $4;
            `, [userId, chatType === 'ACTIVE', limit, offset]);

            // Get recommended contacts from Neo4j
            const neo4jService = Neo4jService.getInstance();
            console.log('neo4jService', neo4jService);
            const recommendedUserReferences = await neo4jService.getRecommendedUsers(userReference, limit, offset);
            // const recommendedUserReferences = "";
            // Fetch user details for recommended contacts
            console.log('recommendedUserReferences', recommendedUserReferences);
            const { rows: recommendedUsers } = await this.pool.query(`
                WITH existing_direct_chats AS (
                    -- Find all direct chats where current user is a member
                    SELECT DISTINCT cm1.chat_id
                    FROM chat_members cm1
                    JOIN chat_members cm2 ON cm1.chat_id = cm2.chat_id
                    JOIN chats c ON c.id = cm1.chat_id
                    WHERE cm1.user_id = $2  -- Current user
                    AND c.chat_type = 'DIRECT'
                    AND cm1.user_id != cm2.user_id  -- Ensure we're matching with other users
                )
                SELECT 
                    u.id,
                    u.username,
                    u.user_icon,
                    u.user_reference as entity_reference,
                    CASE 
                        WHEN u.user_reference LIKE 'U%' THEN 'USER'
                        ELSE 'STORE'
                    END as entity_type
                FROM users u
                WHERE u.user_reference = ANY($1)
                AND NOT EXISTS (
                    -- Exclude users who already have a direct chat with current user
                    SELECT 1 
                    FROM chat_members cm
                    WHERE cm.user_id = u.id
                    AND cm.chat_id IN (SELECT chat_id FROM existing_direct_chats)
                )
            `, [recommendedUserReferences, userId]);

            // Format chats data
            const formattedChats = chats.map(chat => ({
                chat_id: chat.chat_id,
                chat_icon: chat.chat_icon,
                chat_name: chat.chat_name,
                chat_type: chat.chat_type,
                message_access: chat.message_access,
                created_at: chat.created_at,
                updated_at: chat.updated_at,
                entity_reference: chat.entity_reference,
                last_read_sequence: chat.last_read_sequence,
                unread_count: chat.unread_count,
                is_subscribed: chat.is_subscribed,
                role_type: chat.role_type,
                is_muted: chat.is_muted,
                entity_type: chat.entity_type,
                connecting_id: chat.connecting_id,
                chat_preview: chat.chat_preview
            }));

            // Format recommended contacts with default values
            const formattedContacts = recommendedUsers.map(user => ({
                chat_id: "",
                chat_icon: user.user_icon,
                chat_name: user.username,
                chat_type: "direct",
                message_access: null,
                created_at: "",
                updated_at: "",
                entity_reference: user.entity_reference,
                last_read_sequence: 0,
                unread_count: 0,
                is_subscribed: false,
                role_type: null,
                is_muted: false,
                entity_type: user.entity_type,
                connecting_id: user.id,
                chat_preview: null
            }));

            // Combine chats and contacts
            return [...formattedChats, ...formattedContacts];
        } catch (error) {
            console.error(error);
            throw new DatabaseError('Error fetching user chats');
        }
    }

    /**
     * Create a new chat if the creator and member are not already in a chat together
     * @param params CreateChatParams containing chatType, creatorId, and memberIds
     * @returns Newly created chat or existing chat if found
     */
    async createChat(params: CreateChatParams) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            // Check if users are already in a chat together
            const { rows: existingChats } = await client.query(`
                SELECT DISTINCT 
                    c.id as chat_id,
                    c.chat_type,
                    c.created_at,
                    c.updated_at,
                    CAST(c.last_sequence_number AS INTEGER) as last_sequence_number
                FROM chats c
                JOIN chat_members cm1 ON c.id = cm1.chat_id
                JOIN chat_members cm2 ON c.id = cm2.chat_id
                WHERE cm1.user_id = $1 
                AND cm2.user_id = $2
                AND c.chat_type = $3
                LIMIT 1
            `, [params.creatorId, params.memberIds[0], params.chatType]);

            // If chat exists, return it
            if (existingChats.length > 0) {
                await client.query('COMMIT');
                return { chat: existingChats[0], newly_created: false };
            }

            // Create new chat if no existing chat found
            const { rows: [newChat] } = await client.query(`
                INSERT INTO chats (chat_type, chat_name, message_access)
                VALUES ($1, $2, $3)
                RETURNING
                    id as chat_id,
                    chat_type,
                    chat_name,
                    message_access,
                    created_at,
                    updated_at,
                    CAST(last_sequence_number AS INTEGER) as last_sequence_number
            `, [params.chatType, params.chatName || null, params.messageAccess || null]);

            // Add members including creator
            const members = [...new Set([params.creatorId, ...params.memberIds])];
            await Promise.all(members.map(memberId =>
                client.query(`
                    INSERT INTO chat_members (chat_id, user_id, joined_at, is_subscribed)
                    VALUES ($1, $2, CURRENT_TIMESTAMP, true)
                `, [newChat.chat_id, memberId])
            ));

            await client.query('COMMIT');
            return { chat: newChat, newly_created: true };
        } catch (error) {
            await client.query('ROLLBACK');
            console.error(error);
            throw new DatabaseError('Error creating chat');
        } finally {
            client.release();
        }
    }

    async addMembers(chatId: string, memberIds: string[], roleType?: string) {
        if (!memberIds || !Array.isArray(memberIds) || memberIds.length === 0) {
            throw new Error('Invalid or empty memberIds array');
        }

        // Validate roleType if provided
        if (roleType && !['MEMBER', 'ADMIN'].includes(roleType)) {
            throw new Error('Invalid role_type. Must be MEMBER or ADMIN');
        }

        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');

            await Promise.all(memberIds.map(memberId =>
                client.query(`
                    INSERT INTO chat_members (chat_id, user_id, joined_at, role_type)
                    VALUES ($1, $2, CURRENT_TIMESTAMP, $3)
                    ON CONFLICT DO NOTHING
                `, [chatId, memberId, roleType || null])
            ));

            await client.query('COMMIT');
        } catch (error) {
            await client.query('ROLLBACK');
            console.error(error);
            throw new DatabaseError('Error adding chat members');
        } finally {
            client.release();
        }
    }

    async removeMember(chatId: string, userId: string) {
        try {
            await this.pool.query(`
                DELETE FROM chat_members
                WHERE chat_id = $1 AND user_id = $2
            `, [chatId, userId]);
        } catch (error) {
            throw new DatabaseError('Error removing chat member');
        }
    }

    async updateMemberRole(chatId: string, userId: string, roleType: string) {
        try {
            await this.pool.query(`
                UPDATE chat_members
                SET role_type = $3
                WHERE chat_id = $1 AND user_id = $2
            `, [chatId, userId, roleType]);
        } catch (error) {
            throw new DatabaseError('Error updating member role');
        }
    }

    async updateMemberMuteStatus(chatId: string, userId: string, isMuted: boolean) {
        try {
            await this.pool.query(`
                UPDATE chat_members
                SET is_muted = $3
                WHERE chat_id = $1 AND user_id = $2
            `, [chatId, userId, isMuted]);
        } catch (error) {
            throw new DatabaseError('Error updating member mute status');
        }
    }

    /**
     * Get all group chat IDs where the current user is an admin
     * @param userId - The ID of the current user
     * @returns Array of chat IDs where user has ADMIN role
     */
    async getAdminGroupChats(userId: string) {
        try {
            const { rows } = await this.pool.query(`
                SELECT c.id as chat_id,
                c.chat_name
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id
                WHERE cm.user_id = $1
                AND cm.role_type = 'ADMIN'
                AND c.chat_type = 'GROUP'
                ORDER BY c.updated_at DESC
            `, [userId]);

            return rows.map(row => ({
                chat_id: row.chat_id,
                chat_name: row.chat_name,
            }));
        } catch (error) {
            console.error('Error fetching admin group chats:', error);
            throw new DatabaseError('Error fetching admin group chats');
        }
    }

    async deleteChat(chatId: string, userId: string) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            // Verify user is in chat
            const { rows: [member] } = await client.query(`
                SELECT 1 FROM chat_members
                WHERE chat_id = $1 AND user_id = $2
            `, [chatId, userId]);

            if (!member) {
                throw new Error('Unauthorized to delete chat');
            }

            // Delete chat and related data
            await client.query(`DELETE FROM messages WHERE chat_id = $1`, [chatId]);
            await client.query(`DELETE FROM chat_members WHERE chat_id = $1`, [chatId]);
            await client.query(`DELETE FROM chats WHERE id = $1`, [chatId]);

            await client.query('COMMIT');
        } catch (error) {
            await client.query('ROLLBACK');
            throw new DatabaseError('Error deleting chat');
        } finally {
            client.release();
        }
    }

    async getChatByChatId(chatId: string, userId: string) {
        try {
            const { rows: [chat] } = await this.pool.query(`
                WITH chat_preview AS (
                    SELECT DISTINCT ON (m.chat_id)
                        m.chat_id,
                        m.content,
                        m.created_at as message_timestamp,
                        m.sender_id,
                        u.username as sender_username
                    FROM messages m
                    JOIN users u ON m.sender_id = u.id
                    WHERE m.chat_id = $2
                    ORDER BY m.chat_id, m.sequence_number DESC
                ),
                chat_admin AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as admin_user_id
                    FROM chat_members
                    WHERE role_type = 'ADMIN' AND chat_id = $2
                    ORDER BY chat_id, joined_at ASC
                ),
                chat_other_member AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as other_user_id
                    FROM chat_members
                    WHERE user_id <> $1 AND chat_id = $2
                    ORDER BY chat_id, joined_at ASC
                )
                SELECT
                    c.id AS chat_id,
                    c.chat_type,
                    COALESCE(c.chat_name, u.username) AS chat_name,
                    c.message_access,
                    u.user_icon AS chat_icon,
                    u.id AS user_id,
                    u.entity_type,
                    u.user_reference as entity_reference,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    cm.role_type,
                    cm.is_muted::BOOLEAN,
                    c.created_at,
                    c.updated_at,
                    jsonb_build_object(
                        'preview_text',
                        CASE
                            WHEN cp.sender_id = $1 THEN CONCAT('You: ', LEFT(cp.content, 20))
                            ELSE CONCAT(LEFT(cp.content, 20))
                        END,
                        'timestamp', cp.message_timestamp
                    ) as chat_preview
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id AND cm.user_id = $1
                LEFT JOIN chat_admin ca ON c.id = ca.chat_id
                LEFT JOIN chat_other_member com ON c.id = com.chat_id
                JOIN users u ON
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u.id = ca.admin_user_id
                        ELSE u.id = com.other_user_id
                    END
                LEFT JOIN chat_preview cp ON c.id = cp.chat_id
                WHERE c.id = $2
            `, [userId, chatId]);

            if (!chat) return null;

            return {
                chat_id: chat.chat_id,
                chat_icon: chat.chat_icon,
                chat_name: chat.chat_name || 'Group Chat',
                chat_type: chat.chat_type,
                message_access: chat.message_access,
                user_id: chat.user_id,
                created_at: chat.created_at,
                updated_at: chat.updated_at,
                last_read_sequence: chat.last_read_sequence,
                unread_count: chat.unread_count,
                is_subscribed: chat.is_subscribed,
                role_type: chat.role_type,
                is_muted: chat.is_muted,
                entity_type: chat.entity_type,
                entity_reference: chat.entity_reference,
                chat_preview: chat.chat_preview
            };
        } catch (error) {
            console.error(error);
            throw new DatabaseError('Error fetching chat');
        }
    }

    async updateChat(chatId: string, updateData: any, userId: string) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');

            // Verify user is in chat
            const { rows: [member] } = await client.query(`
                SELECT 1 FROM chat_members
                WHERE chat_id = $1 AND user_id = $2
            `, [chatId, userId]);

            if (!member) {
                throw new Error('Unauthorized to update chat');
            }

            // Update chat
            const { rows: [updatedChat] } = await client.query(`
                UPDATE chats
                SET updated_at = NOW(),
                    chat_type = COALESCE($1, chat_type),
                    chat_name = COALESCE($3, chat_name),
                    message_access = COALESCE($4, message_access)
                WHERE id = $2
                RETURNING *
            `, [updateData.chatType, chatId, updateData.chatName, updateData.messageAccess]);

            await client.query('COMMIT');
            return updatedChat;
            
        } catch (error) {
            await client.query('ROLLBACK');
            throw new DatabaseError('Error updating chat');
        } finally {
            client.release();
        }
    }

    async checkDirectChatExists(currentUserId: string, userId: string) {
        try {
            const { rows: [chat] } = await this.pool.query(`
                WITH direct_chat AS (
                    SELECT c.id
                    FROM chats c
                    JOIN chat_members cm1 ON c.id = cm1.chat_id AND cm1.user_id = $1
                    JOIN chat_members cm2 ON c.id = cm2.chat_id AND cm2.user_id = $2
                    WHERE c.chat_type = 'DIRECT'
                    LIMIT 1
                ),
                chat_preview AS (
                    SELECT DISTINCT ON (m.chat_id)
                        m.chat_id,
                        m.content,
                        m.created_at as message_timestamp,
                        m.sender_id,
                        u.username as sender_username
                    FROM messages m
                    JOIN users u ON m.sender_id = u.id
                    WHERE m.chat_id IN (SELECT id FROM direct_chat)
                    ORDER BY m.chat_id, m.sequence_number DESC
                ),
                chat_other_member AS (
                    SELECT DISTINCT ON (chat_id)
                        chat_id,
                        user_id as other_user_id
                    FROM chat_members
                    WHERE user_id = $2 AND chat_id IN (SELECT id FROM direct_chat)
                    ORDER BY chat_id, joined_at ASC
                )
                SELECT
                    c.id AS chat_id,
                    c.chat_type,
                    COALESCE(c.chat_name, u.username) AS chat_name,
                    c.message_access,
                    u.user_icon AS chat_icon,
                    u.id AS user_id,
                    u.entity_type,
                    u.user_reference as entity_reference,
                    cm.last_read_sequence::INTEGER,
                    cm.unread_count::INTEGER,
                    cm.is_subscribed::BOOLEAN,
                    cm.role_type,
                    cm.is_muted::BOOLEAN,
                    c.created_at,
                    c.updated_at,
                    jsonb_build_object(
                        'preview_text',
                        CASE
                            WHEN cp.sender_id = $1 THEN CONCAT('You: ', LEFT(cp.content, 20))
                            ELSE CONCAT(LEFT(cp.content, 20))
                        END,
                        'timestamp', cp.message_timestamp
                    ) as chat_preview
                FROM chats c
                JOIN chat_members cm ON c.id = cm.chat_id AND cm.user_id = $1
                LEFT JOIN chat_other_member com ON c.id = com.chat_id
                JOIN users u ON u.id = com.other_user_id
                LEFT JOIN chat_preview cp ON c.id = cp.chat_id
                WHERE c.id IN (SELECT id FROM direct_chat)
            `, [currentUserId, userId]);

            if (!chat) return null;

            return {
                chat_id: chat.chat_id,
                chat_icon: chat.chat_icon,
                chat_name: chat.chat_name || 'Direct Chat',
                chat_type: chat.chat_type,
                message_access: chat.message_access,
                user_id: chat.user_id,
                created_at: chat.created_at,
                updated_at: chat.updated_at,
                last_read_sequence: chat.last_read_sequence,
                unread_count: chat.unread_count,
                is_subscribed: chat.is_subscribed,
                role_type: chat.role_type,
                is_muted: chat.is_muted,
                entity_type: chat.entity_type,
                entity_reference: chat.entity_reference,
                chat_preview: chat.chat_preview
            };
        } catch (error) {
            console.error(error);
            throw new DatabaseError('Error checking direct chat');
        }
    }

    async chatSearch(searchQuery: string, limit: number, offset: number, userId: string) {
        try {
            console.log(searchQuery, limit, offset, userId);
            const { rows: chats } = await this.pool.query(`
            WITH user_chats AS (
                SELECT
                    c.id AS chat_id,
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u_admin.id
                        ELSE u.id
                    END AS user_id,
                    u.user_reference as entity_reference,
                    COALESCE(c.chat_name, u.username) AS chat_name,
                    c.message_access,
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u_admin.user_icon
                        ELSE u.user_icon
                    END AS chat_icon,
                    CASE
                        WHEN c.chat_type = 'GROUP' THEN u_admin.entity_type
                        ELSE u.entity_type
                    END AS entity_type,
                    COALESCE(cm2.last_read_sequence, 0)::INTEGER as last_read_sequence,
                    COALESCE(cm2.unread_count, 0)::INTEGER as unread_count,
                    COALESCE(cm2.is_subscribed, false)::BOOLEAN as is_subscribed,
                    cm2.role_type,
                    COALESCE(cm2.is_muted, false)::BOOLEAN as is_muted,
                    c.created_at,
                    c.updated_at,
                    c.chat_type,
                    CASE
                        WHEN u.username ILIKE $2 THEN 4                   -- Exact match
                        WHEN u.username ILIKE $2 || '%' THEN 3           -- Starts with
                        WHEN u.username ILIKE '%' || $2 || '%' THEN 2    -- Contains
                        WHEN COALESCE(u.name, '') ILIKE $2 || '%' THEN 1 -- Name starts with
                        ELSE 0
                    END as match_type
                FROM users u
                LEFT JOIN (
                    SELECT DISTINCT ON (cm1.user_id) cm1.chat_id, cm1.user_id
                    FROM chat_members cm1
                    JOIN chat_members cm2 ON cm1.chat_id = cm2.chat_id
                    WHERE cm2.user_id = $1 AND cm1.user_id != $1
                ) cm ON cm.user_id = u.id
                LEFT JOIN chats c ON c.id = cm.chat_id
                LEFT JOIN chat_members cm2 ON cm2.chat_id = c.id AND cm2.user_id = $1
                LEFT JOIN chat_members cm_admin ON c.id = cm_admin.chat_id AND cm_admin.role_type = 'ADMIN'
                LEFT JOIN users u_admin ON cm_admin.user_id = u_admin.id
                WHERE (u.username ILIKE '%' || $2 || '%' OR u.name ILIKE '%' || $2 || '%') AND u.id != $1
            ),
            chat_preview AS (
                SELECT DISTINCT ON (m.chat_id)
                    m.chat_id,
                    m.content,
                    m.created_at as message_timestamp,
                    m.sender_id,
                    u.username as sender_username
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.chat_id IN (SELECT chat_id FROM user_chats WHERE chat_id IS NOT NULL)
                ORDER BY m.chat_id, m.sequence_number DESC
            )
            SELECT
                COALESCE(uc.chat_id::TEXT, NULL) as chat_id,
                uc.chat_type,
                uc.chat_name,
                uc.message_access,
                uc.chat_icon,
                uc.user_id,
                uc.entity_reference,
                uc.entity_type,
                uc.last_read_sequence,
                uc.unread_count,
                uc.is_subscribed,
                uc.role_type,
                uc.is_muted,
                uc.created_at,
                uc.updated_at,
                CASE 
                    WHEN uc.chat_id IS NOT NULL THEN NULL
                    ELSE uc.user_id::TEXT
                END as connecting_id,
                CASE 
                    WHEN uc.chat_id IS NOT NULL THEN
                        jsonb_build_object(
                            'preview_text', 
                            CASE 
                                WHEN cp.sender_id = $1 THEN CONCAT('You: ', LEFT(cp.content, 20))
                                ELSE CONCAT(LEFT(cp.content, 20))
                            END,
                            'timestamp', cp.message_timestamp
                        )
                    ELSE NULL
                END as chat_preview
            FROM user_chats uc
            LEFT JOIN chat_preview cp ON uc.chat_id = cp.chat_id
            ORDER BY 
                uc.match_type DESC,
                LENGTH(uc.chat_name) ASC
            LIMIT $3 OFFSET $4
            `, [userId, searchQuery.replace(/[%_]/g, ''), limit, offset]);
            console.log(chats);
            return chats;
        } catch (error) {
            console.error(error);
            throw new DatabaseError('Error searching chats');
        }
    }

}
