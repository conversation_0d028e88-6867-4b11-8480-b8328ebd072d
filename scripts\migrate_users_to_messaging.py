import psycopg2
import random
import string
import requests
import json
from typing import Dict, Any
from decouple import config

# Configuration
MESSAGING_SERVICE_URL = config('MESSAGING_SERVICE_URL')
DB_CONFIG = {
    'dbname': config('SWADESIC_DB_NAME'),
    'user': config('SWADESIC_DB_USER'),
    'password': config('SWADESIC_DB_PASSWORD'),
    'host': config('SWADESIC_DB_HOST'),
    'port': config('SWADESIC_DB_PORT')
}

def generate_password():
    chars = string.ascii_letters + string.digits + string.punctuation
    return ''.join(random.choice(chars) for _ in range(12))

def connect_to_db():
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        raise

def fetch_users(conn):
    cursor = conn.cursor()
    cursor.execute("""
        SELECT user_reference, user_name, display_name, icon 
        FROM "user"."user" 
        WHERE deleted = false 
        AND user_name IS NOT NULL
        AND new_messaging_token IS NULL
    """)
    return cursor.fetchall()

def fetch_stores(conn):
    cursor = conn.cursor()
    cursor.execute("""
        SELECT store_reference, storehandle, store_name, icon 
        FROM "store"."store" 
        WHERE deleted = false 
        AND new_messaging_token IS NULL
    """)
    return cursor.fetchall()


def fetch_active_stores(conn):
    cursor = conn.cursor()
    cursor.execute("""
        SELECT store_reference, storehandle, store_name, icon 
        FROM "store"."store" 
        WHERE deleted = false 
        AND is_active = true 
        AND store_ai_messaging_token IS NULL
        AND storehandle IS NOT NULL
    """)
    return cursor.fetchall()

def register_user(data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # Register user
        register_response = requests.post(
            f"{MESSAGING_SERVICE_URL}/api/auth/register",
            json=data
        )
        register_response.raise_for_status()
        
        new_messaging_user_id = register_response.json().get("user", {}).get("id")

        # Login user to get token
        login_data = {
            "email": data["email"],
            "password": data["password"]
        }
        login_response = requests.post(
            f"{MESSAGING_SERVICE_URL}/api/auth/login",
            json=login_data
        )
        print(login_response.json())
        login_response.raise_for_status()

        return {
            "token": login_response.json().get('token'),
            "user_id": login_response.json().get('user', {}).get('messaging_user_id'),
            "reference": login_response.json().get('user', {}).get('user_reference')
        }
    except Exception as e:
        print(f"Error registering/logging in {data['user_reference']}: {str(e)}")
        return None

def update_user_token(conn, reference: str, token: str, user_id: str):
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE "user"."user" 
        SET new_messaging_token = %s, new_messaging_user_id = %s
        WHERE user_reference = %s
    """, (token, user_id, reference))
    conn.commit()

def update_store_token(conn, reference: str, token: str, user_id: str):
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE "store"."store" 
        SET new_messaging_token = %s, new_messaging_user_id = %s
        WHERE store_reference = %s
    """, (token, user_id, reference))
    conn.commit()

def update_store_ai_token(conn, reference: str, token: str, user_id: str, sai_reference: str):
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE "store"."store" 
        SET store_ai_messaging_token = %s, store_ai_messaging_user_id = %s, store_ai_reference = %s
        WHERE store_reference = %s
    """, (token, user_id, sai_reference,reference))
    conn.commit()

def main():
    conn = connect_to_db()
    
    try:
        # Process Users
        users = fetch_users(conn)
        print(f"Found {len(users)} users to migrate")
        
        for user in users:
            user_reference, username, display_name, icon = user
            password = generate_password()
            
            register_data = {
                "username": username or user_reference,
                "email": f"{user_reference}@swadesic.com",
                "password": password,
                "user_reference": user_reference,
                "user_icon": icon if icon else None,
                "name": display_name
            }
            
            result = register_user(register_data)
            print(result)
            if result:
                update_user_token(conn, result["reference"], result["token"], result["user_id"])
                print(f"Successfully migrated user: {user_reference}")

        # Process Stores
        stores = fetch_stores(conn)
        print(f"\nFound {len(stores)} stores to migrate")
        
        for store in stores:
            store_reference, storehandle, store_name, icon = store
            password = generate_password()
            store_email = f"{store_reference}@swadesic.com"
            
            register_data = {
                "username": storehandle or store_reference,
                "email": store_email,
                "password": password,
                "user_reference": store_reference,
                "user_icon": icon if icon else None,
                "name": store_name
            }
            
            result = register_user(register_data)
            print(result)
            if result:
                update_store_token(conn, result["reference"], result["token"], result["user_id"])
                print(f"Successfully migrated store: {store_reference}")
        
        # Process Store Ai Users
        # stores = fetch_active_stores(conn)
        # print(f"\nFound {len(stores)} stores to migrate")
        
        # for store in stores:
        #     store_reference, storehandle, store_name, icon = store
        #     password = generate_password()
        #     store_email = f"{store_reference.replace('S', 'SAI_')}@swadesic.com"
            
        #     register_data = {
        #         "username": storehandle + "-storeAI",
        #         "email": store_email,
        #         "password": password,
        #         "user_reference": store_reference.replace('S', 'SAI_'),
        #         "user_icon": icon if icon else None,
        #         "name": store_name + " Store AI"
        #     }
            
        #     result = register_user(register_data)
        #     print(result)
        #     if result:
        #         update_store_ai_token(conn, store_reference, result["token"], result["user_id"], result["reference"])
        #         print(f"Successfully migrated store: {store_reference}")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
