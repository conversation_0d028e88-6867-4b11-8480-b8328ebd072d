module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.addColumn('users', 'user_reference', {
            type: Sequelize.STRING,
            allowNull: true,
            unique: true,
            comment: 'Identifier for the main Swadesic project'
        });

        await queryInterface.addColumn('users', 'user_icon', {
            type: Sequelize.STRING,
            allowNull: true,
            comment: 'URL or path to user profile icon'
        });

        await queryInterface.addColumn('users', 'name', {
            type: Sequelize.STRING,
            allowNull: true,
            comment: 'Full name of the user'
        });
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.removeColumn('users', 'user_reference');
        await queryInterface.removeColumn('users', 'user_icon');
        await queryInterface.removeColumn('users', 'name');
    }
};
