import * as express from 'express';
// import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import hpp from 'hpp';
import xss from 'xss';

export const setupSecurity = (app: express.Application) => {
  // Rate limiting - prevents brute force attacks
  // const limiter = rateLimit({
  //   windowMs: 15 * 60 * 1000, // 15 minutes
  //   max: 100, // limit each IP to 100 requests per windowMs
  //   message: 'Too many requests from this IP, please try again later.'
  // });

  // Apply security middleware
  app.use(helmet()); // Sets various HTTP headers for security
  app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
    if (req.body) {
        req.body = xss(req.body); // Sanitize the request body
    }
    next();
  });
  app.use(hpp()); // Prevent HTTP Parameter Pollution attacks
  // app.use('/api', limiter);
  
  // CORS configuration
  app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
    res.header('Access-Control-Allow-Origin', process.env.ALLOWED_ORIGINS || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    next();
  });
};