import Joi from 'joi';

export const validateDeviceToken = (data: any) => {
    const schema = Joi.object({
        device_id: Joi.string()
            .max(30)
            .required()
            .messages({
                'string.empty': 'Device ID is required',
                'string.max': 'Device ID must not exceed 30 characters',
                'any.required': 'Device ID is required'
            }),
        fcm_token: Joi.string()
            .max(300)
            .required()
            .messages({
                'string.empty': 'FCM token is required',
                'string.max': 'FCM token must not exceed 300 characters',
                'any.required': 'FCM token is required'
            }),
        user_app_version: Joi.string()
            .max(10)
            .optional()
            .messages({
                'string.max': 'App version must not exceed 10 characters'
            })
    });

    return schema.validate(data);
};

export const validateTestNotification = (data: any) => {
    const schema = Joi.object({
        title: Joi.string()
            .max(100)
            .required()
            .messages({
                'string.empty': 'Title is required',
                'string.max': 'Title must not exceed 100 characters',
                'any.required': 'Title is required'
            }),
        body: Joi.string()
            .max(500)
            .required()
            .messages({
                'string.empty': 'Body is required',
                'string.max': 'Body must not exceed 500 characters',
                'any.required': 'Body is required'
            })
    });

    return schema.validate(data);
};
