'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add entity_type column
    await queryInterface.addColumn('users', 'entity_type', {
      type: Sequelize.STRING(10),
      allowNull: true,
      defaultValue: 'USER'
    });

    // Update existing records based on user_reference prefix from users table
    await queryInterface.sequelize.query(`
      UPDATE users 
      SET entity_type = CASE 
          WHEN user_reference LIKE 'S%' THEN 'STORE'
          WHEN user_reference LIKE 'U%' THEN 'USER'
          ELSE 'USER'  -- Default to USER for any unmatched patterns
      END;
    `);

    // Make sure there are no remaining nulls (for any records that might not have a matching user)
    await queryInterface.sequelize.query(`
      UPDATE users 
      SET entity_type = 'USER'
      WHERE entity_type IS NULL
    `);

    // Make entity_type NOT NULL
    await queryInterface.changeColumn('users', 'entity_type', {
      type: Sequelize.STRING(10),
      allowNull: false
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('users', 'entity_type');
  }
};
