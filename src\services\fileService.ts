import { S3 } from 'aws-sdk';
import { v4 as uuidv4 } from 'uuid';
import { validateEnv } from '../config/env';

export class FileService {
  private s3: S3;
  private bucket: string;

  constructor() {
    const env = validateEnv();
    this.s3 = new S3({
      accessKeyId: env.AWS_ACCESS_KEY_ID,
      secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
      region: env.AWS_REGION
    });
    this.bucket = env.AWS_S3_BUCKET;
  }

  async uploadFile(file: Buffer, mimeType: string): Promise<string> {
    const key = `uploads/${uuidv4()}`;
    await this.s3.putObject({
      Bucket: this.bucket,
      Key: key,
      Body: file,
      ContentType: mimeType
    }).promise();

    return `https://${this.bucket}.s3.amazonaws.com/${key}`;
  }

  async deleteFile(fileUrl: string): Promise<void> {
    const key = fileUrl.split('.com/')[1];
    await this.s3.deleteObject({
      Bucket: this.bucket,
      Key: key
    }).promise();
  }
}