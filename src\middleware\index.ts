import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
// import rateLimit from 'express-rate-limit';
import path from 'path';
import { authMiddleware } from './auth';

export const setupMiddleware = (app: express.Application) => {
  app.use(helmet());
  app.use(cors());
  app.use(express.json());
  
  // Serve static files from uploads directory with authentication
  app.use('/uploads', authMiddleware, express.static(path.join(process.cwd(), 'uploads')));
  
  // // Rate limiting
  // const limiter = rateLimit({
  //   windowMs: 15 * 60 * 1000, // 15 minutes
  //   max: 100 // limit each IP to 100 requests per windowMs
  // });
  // app.use(limiter);
};