{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node", "express"], "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist"]}