import jwt, { Secret } from 'jsonwebtoken';
import { config } from '../config/env';
import { AuthenticationError } from './errors';

export const generateToken = (userId: string): string => {
    const secret = config.jwt.secret;
    if (!secret) {
        throw new Error('JWT secret is not configured');
    }

    return jwt.sign(
        { id: userId},
        secret as Secret,
    );
};

export const verifyToken = (token: string): Promise<any> => {
    return new Promise((resolve, reject) => {
        jwt.verify(token, config.jwt.secret, (err, decoded) => {
            if (err) {
                reject(new AuthenticationError());
            }
            resolve(decoded);
        });
    });
};